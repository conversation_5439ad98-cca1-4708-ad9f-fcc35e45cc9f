import {
  ClockCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  SettingOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Al<PERSON>,
  Button,
  Col,
  Row,
  Space,
  Spin,
  Typography,
} from 'antd';
import { ProCard } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserProfileDetailResponse, UserPersonalStatsResponse } from '@/types/api';
import UnifiedSettingsModal from './UnifiedSettingsModal';

const { Title, Text } = Typography;

/**
 * 个人信息组件
 *
 * 显示用户的完整个人信息，包括基本信息和登录历史。
 * 整合了原UserProfileCard和LastLoginInfo组件的功能。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名、邮箱、电话
 * 3. 显示注册日期
 * 4. 显示最后登录时间和登录团队
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */
const PersonalInfo: React.FC = () => {
  /**
   * 用户详细信息状态管理
   */
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: '',
    position: '',
    email: '',
    telephone: '',
    registerDate: '',
    lastLoginTime: '',
    lastLoginTeam: '',
    teamCount: 0,
    avatar: '',
  });

  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // 数据概览状态管理
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // Modal状态管理
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  // 获取用户数据和统计数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userDetail = await UserService.getUserProfileDetail();
        setUserInfo(userDetail);
        setUserInfoError(null);
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
        setUserInfoError('获取用户详细信息失败，请稍后重试');
      } finally {
        setUserInfoLoading(false);
      }
    };

    const fetchStatsData = async () => {
      try {
        const stats = await UserService.getUserPersonalStats();
        setPersonalStats(stats);
        setStatsError(null);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        setStatsError('获取统计数据失败，请稍后重试');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchUserData();
    fetchStatsData();
  }, []);

  return (
    <ProCard
      title="个人信息"
      extra={
        <Button
          size="small"
          icon={<SettingOutlined />}
          onClick={() => setSettingsModalVisible(true)}
          style={{
            borderRadius: 6,
            width: 28,
            height: 28,
            padding: 0,
            border: '1px solid #d9d9d9',
            color: '#595959',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        />
      }
      style={{
        marginBottom: 16,
        borderRadius: 8,
      }}
      // styles={{
      //   header: {
      //     borderBottom: '1px solid #f0f0f0',
      //     paddingBottom: 12,
      //   },
      //   body: {
      //     padding: '16px',
      //   },
      // }}
    >
      {userInfoError ? (
        <Alert
          message="个人信息加载失败"
          description={userInfoError}
          type="error"
          showIcon
        />
      ) : (
        <Spin spinning={userInfoLoading || statsLoading}>
          {/* 整合的个人信息和数据概览区域 */}
          <Row gutter={[24, 16]}>
            {/* 左侧：个人信息 */}
            <Col xs={24} sm={24} md={14} lg={14} xl={14}>
              <div>
                {/* 第一行：姓名 */}
                <div style={{ marginBottom: '8px' }}>
                  <Title
                    level={4}
                    style={{
                      margin: 0,
                      fontSize: 18,
                      fontWeight: 600,
                      color: '#262626',
                      lineHeight: 1.3,
                    }}
                  >
                    {userInfo.name || '加载中...'}
                  </Title>
                </div>

                  {/* 第二行：联系信息 */}
                  <div style={{ marginBottom: '8px' }}>
                    <Space size={16} wrap>
                      {userInfo.email && (
                        <Space size={6} align="center">
                          <MailOutlined
                            style={{
                              fontSize: 14,
                              color: '#1890ff',
                            }}
                          />
                          <Text
                            style={{
                              color: '#595959',
                              fontSize: 13,
                              fontWeight: 500,
                            }}
                          >
                            {userInfo.email}
                          </Text>
                        </Space>
                      )}
                      {userInfo.telephone && (
                        <Space size={6} align="center">
                          <PhoneOutlined
                            style={{
                              fontSize: 14,
                              color: '#52c41a',
                            }}
                          />
                          <Text
                            style={{
                              color: '#595959',
                              fontSize: 13,
                              fontWeight: 500,
                            }}
                          >
                            {userInfo.telephone}
                          </Text>
                        </Space>
                      )}
                      {/* 注册日期 */}
                      {userInfo.registerDate && (
                        <Space size={4} align="center">
                          <Text
                            style={{
                              fontSize: 12,
                              color: '#8c8c8c',
                              fontWeight: 500,
                            }}
                          >
                            📅 注册于 {userInfo.registerDate}
                          </Text>
                        </Space>
                      )}
                    </Space>
                  </div>

                  {/* 第三行：登录信息 */}
                  <div>
                    <Space size={16} wrap>
                      {/* 最后登录时间 */}
                      {userInfo.lastLoginTime && (
                        <Space size={4} align="center">
                          <ClockCircleOutlined
                            style={{
                              fontSize: 12,
                              color: '#1890ff',
                            }}
                          />
                          <Text
                            style={{
                              fontSize: 12,
                              color: '#8c8c8c',
                              fontWeight: 500,
                            }}
                          >
                            最后登录：{userInfo.lastLoginTime}
                          </Text>
                        </Space>
                      )}

                      {/* 最后登录团队 */}
                      {userInfo.lastLoginTeam && (
                        <Space size={4} align="center">
                          <TeamOutlined
                            style={{
                              fontSize: 12,
                              color: '#52c41a',
                            }}
                          />
                          <Text
                            style={{
                              fontSize: 12,
                              color: '#8c8c8c',
                              fontWeight: 500,
                            }}
                          >
                            团队：{userInfo.lastLoginTeam}
                          </Text>
                        </Space>
                      )}
                    </Space>
                  </div>
                </div>
              </div>
            </Col>

            {/* 右侧：数据概览 */}
            <Col xs={24} sm={24} md={10} lg={10} xl={10}>
              {statsError ? (
                <Alert
                  message="数据概览加载失败"
                  description={statsError}
                  type="error"
                  showIcon
                  size="small"
                />
              ) : (
                <div>
                  <div style={{ marginBottom: 12 }}>
                    <Text style={{ fontSize: 14, fontWeight: 600, color: '#262626' }}>
                      数据概览
                    </Text>
                  </div>
                  <Row gutter={[8, 8]}>
                    {/* 车辆统计 */}
                    <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                      <div style={{ textAlign: 'center', padding: '12px 8px', background: '#f8f9fa', borderRadius: 6 }}>
                        <div
                          style={{
                            fontSize: 20,
                            fontWeight: 700,
                            color: '#1890ff',
                            lineHeight: 1,
                            marginBottom: 4,
                          }}
                        >
                          {personalStats.vehicles}
                        </div>
                        <div
                          style={{
                            fontSize: 11,
                            color: '#8c8c8c',
                            fontWeight: 500,
                          }}
                        >
                          车辆
                        </div>
                      </div>
                    </Col>

                    {/* 人员统计 */}
                    <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                      <div style={{ textAlign: 'center', padding: '12px 8px', background: '#f8f9fa', borderRadius: 6 }}>
                        <div
                          style={{
                            fontSize: 20,
                            fontWeight: 700,
                            color: '#52c41a',
                            lineHeight: 1,
                            marginBottom: 4,
                          }}
                        >
                          {personalStats.personnel}
                        </div>
                        <div
                          style={{
                            fontSize: 11,
                            color: '#8c8c8c',
                            fontWeight: 500,
                          }}
                        >
                          人员
                        </div>
                      </div>
                    </Col>

                    {/* 预警统计 */}
                    <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                      <div style={{ textAlign: 'center', padding: '12px 8px', background: '#f8f9fa', borderRadius: 6 }}>
                        <div
                          style={{
                            fontSize: 20,
                            fontWeight: 700,
                            color: '#faad14',
                            lineHeight: 1,
                            marginBottom: 4,
                          }}
                        >
                          {personalStats.warnings}
                        </div>
                        <div
                          style={{
                            fontSize: 11,
                            color: '#8c8c8c',
                            fontWeight: 500,
                          }}
                        >
                          预警
                        </div>
                      </div>
                    </Col>

                    {/* 告警统计 */}
                    <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                      <div style={{ textAlign: 'center', padding: '12px 8px', background: '#f8f9fa', borderRadius: 6 }}>
                        <div
                          style={{
                            fontSize: 20,
                            fontWeight: 700,
                            color: '#ff4d4f',
                            lineHeight: 1,
                            marginBottom: 4,
                          }}
                        >
                          {personalStats.alerts}
                        </div>
                        <div
                          style={{
                            fontSize: 11,
                            color: '#8c8c8c',
                            fontWeight: 500,
                          }}
                        >
                          告警
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              )}
            </Col>
          </Row>
        </Spin>
      )}

      {/* 统一设置Modal */}
      <UnifiedSettingsModal
        visible={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        userInfo={userInfo}
        onSuccess={() => {
          // 可以在这里刷新用户信息或团队列表
          console.log('设置操作成功');
        }}
      />
    </ProCard>
  );
};

export default PersonalInfo;
