{"cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "pkg": {"name": "<PERSON><PERSON><PERSON>-frontend", "version": "1.0.0", "private": true, "description": "TeamAuth - 现代化的团队协作与管理平台", "repository": "**************:teamauth/teamauth.git", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "biome:lint": "npx @biomejs/biome lint", "build": "max build", "dev": "npm run start:dev", "postinstall": "max setup", "lint": "npm run biome:lint && npm run tsc", "lint-staged": "lint-staged", "openapi": "max openapi", "prepare": "husky", "preview": "npm run build && max preview --port 8000", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "tsc": "tsc --noEmit"}, "browserslist": ["defaults"], "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.7.19", "@ant-design/v5-patch-for-react-19": "^1.0.3", "antd": "^5.25.4", "antd-style": "^3.7.0", "dayjs": "^1.11.13", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.0.1", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.10", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@umijs/max": "^4.3.24", "cross-env": "^7.0.3", "express": "^4.21.1", "gh-pages": "^6.1.1", "husky": "^9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^16.1.2", "mockjs": "^1.1.0", "ts-node": "^10.9.2", "typescript": "^5.6.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=20.0.0"}}, "pkgPath": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "preset", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 41}, "enableBy": "register", "type": "preset", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/max/dist/preset": {"config": {}, "time": {"hooks": {}, "register": 18}, "enableBy": "register", "type": "preset", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/max/dist/preset.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/max/dist/preset", "key": "preset"}, "umi-presets-pro": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 10}, "enableBy": "register", "type": "preset", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/umi-presets-pro/dist/index.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "umi-presets-pro", "key": "umiPresetsPro"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 10}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [2]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [55]}, "register": 75}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [1]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [1]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 23}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\react", "react-dom": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\react-dom", "react-router": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\react-router", "react-router-dom": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 243}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 308}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 54}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [1]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {}, "register": 98}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {"modifyAppData": [0], "onCheck": [0]}, "register": 3}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [2]}, "register": 7}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 14}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 8}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 14}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 13}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 11}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 20}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 67}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 10}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {"modifyConfig": [1], "onStart": [4]}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 8}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 6}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 14}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 154}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {"modifyAppData": [91], "onStart": [0]}, "register": 139}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 80}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 10}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/plugins/dist/access": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/access.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/access", "key": "access"}, "./node_modules/@umijs/plugins/dist/analytics": {"config": {"onChange": "reload"}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/analytics.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/analytics", "key": "analytics"}, "./node_modules/@umijs/plugins/dist/antd": {"config": {}, "time": {"hooks": {"modifyConfig": [4], "modifyAppData": [0]}, "register": 15}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/antd.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/antd", "key": "antd"}, "./node_modules/@umijs/plugins/dist/dva": {"config": {}, "time": {"hooks": {}, "register": 24}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/dva.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/dva", "key": "dva"}, "./node_modules/@umijs/plugins/dist/initial-state": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/initial-state.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/initial-state", "key": "initialState"}, "./node_modules/@umijs/plugins/dist/layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {"modifyConfig": [0], "addLayouts": [0], "modifyAppData": [1]}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/layout.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/layout", "key": "layout"}, "./node_modules/@umijs/plugins/dist/locale": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/locale.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/locale", "key": "locale"}, "./node_modules/@umijs/plugins/dist/mf": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/mf.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/mf", "key": "mf"}, "./node_modules/@umijs/plugins/dist/model": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/model.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/model", "key": "model"}, "./node_modules/@umijs/plugins/dist/moment2dayjs": {"config": {}, "time": {"hooks": {"modifyConfig": [2]}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/moment2dayjs.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/moment2dayjs", "key": "moment2dayjs"}, "./node_modules/@umijs/plugins/dist/qiankun": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/qiankun.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/qiankun", "key": "qiankun"}, "./node_modules/@umijs/plugins/dist/qiankun/master": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/qiankun/master.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/qiankun/master", "key": "<PERSON><PERSON><PERSON>n-master"}, "./node_modules/@umijs/plugins/dist/qiankun/slave": {"config": {}, "time": {"hooks": {}, "register": 7}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/qiankun/slave.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/qiankun/slave", "key": "qiankun-slave"}, "./node_modules/@umijs/plugins/dist/react-query": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/react-query.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/react-query", "key": "reactQuery"}, "./node_modules/@umijs/plugins/dist/request": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/request.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/request", "key": "request"}, "./node_modules/@umijs/plugins/dist/styled-components": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/styled-components.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/styled-components", "key": "styledComponents"}, "./node_modules/@umijs/plugins/dist/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/tailwindcss.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/tailwindcss", "key": "tailwindcss"}, "./node_modules/@umijs/plugins/dist/valtio": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/dist/valtio.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/plugins/dist/valtio", "key": "valtio"}, "./node_modules/@umijs/max/dist/plugins/maxAlias": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/max/dist/plugins/maxAlias.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/max/dist/plugins/maxAlias", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/max/dist/plugins/maxAppData": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/max/dist/plugins/maxAppData.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/max/dist/plugins/maxAppData", "key": "maxAppData"}, "./node_modules/@umijs/max/dist/plugins/maxChecker": {"config": {}, "time": {"hooks": {"onCheckPkgJSON": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/max/dist/plugins/maxChecker.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/max/dist/plugins/maxChecker", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/umi-presets-pro/dist/features/proconfig": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/umi-presets-pro/dist/features/proconfig.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/umi-presets-pro/dist/features/proconfig", "key": "proconfig"}, "./node_modules/umi-presets-pro/dist/features/maxtabs": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/umi-presets-pro/dist/features/maxtabs.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/umi-presets-pro/dist/features/maxtabs", "key": "maxtabs"}, "@umijs/max-plugin-openapi": {"config": {}, "time": {"hooks": {}, "register": 610}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/max-plugin-openapi/dist/index.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "@umijs/max-plugin-openapi", "key": "openAPI"}, "./node_modules/@alita/plugins/dist/keepalive": {"config": {}, "time": {"hooks": {}, "register": 83}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@alita/plugins/dist/keepalive.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@alita/plugins/dist/keepalive", "key": "keepalive"}, "./node_modules/@alita/plugins/dist/tabs-layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@alita/plugins/dist/tabs-layout.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@alita/plugins/dist/tabs-layout", "key": "tabsLayout"}, "@umijs/request-record": {"config": {"default": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}}, "time": {"hooks": {"modifyConfig": [0], "modifyAppData": [0]}, "register": 185}, "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/request-record/dist/cjs/index.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "@umijs/request-record", "key": "requestRecord"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "dev", "args": {"_": []}, "userConfig": {"hash": true, "publicPath": "/", "routes": [{"path": "/user", "layout": false, "routes": [{"name": "login", "path": "/user/login", "component": "./user/login"}]}, {"path": "/invite/:token", "name": "团队邀请", "component": "./invite/[token]", "layout": false}, {"path": "/dashboard", "name": "仪表盘", "icon": "dashboard", "component": "./Dashboard"}, {"path": "/personal-center", "name": "个人中心", "icon": "user", "component": "./personal-center", "layout": false}, {"path": "/settings", "name": "设置", "icon": "setting", "component": "./settings", "layout": false}, {"path": "/help", "name": "帮助中心", "icon": "question", "component": "./help", "hideInMenu": true}, {"path": "/", "redirect": "/dashboard"}, {"path": "*", "layout": false, "component": "./404"}], "ignoreMomentLocale": true, "proxy": {"/api/": {"target": "http://localhost:8080", "changeOrigin": true, "pathRewrite": {"^/api/": "/api/v1/"}}}, "fastRefresh": true, "model": {}, "initialState": {}, "title": "团队协作管理系统", "layout": {"locale": false, "navTheme": "light", "colorPrimary": "#1890ff", "layout": "side", "contentWidth": "Fluid", "fixedHeader": false, "fixSiderbar": true, "colorWeak": false, "title": "团队协作管理系统", "pwa": false, "logo": "/logo.svg", "iconfontUrl": "", "token": {}}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "antd": {"appConfig": {}, "configProvider": {"theme": {"cssVar": true, "token": {"fontFamily": "AlibabaSans, sans-serif"}}}}, "request": {}, "access": {}, "headScripts": [{"src": "\\scripts\\loading.js", "async": true}], "presets": ["umi-presets-pro"], "mako": {}, "esbuildMinifyIIFE": true, "requestRecord": {}, "exportStatic": {}}, "mainConfigFile": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\config\\config.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/", "svgr": {}, "publicPath": "/", "mfsu": false, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\react", "react-dom": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\react-dom", "react-router": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\react-router", "react-router-dom": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\react-router-dom", "@": "H:/projects/IdeaProjects/teamAuth/frontend/src", "@@": "H:/projects/IdeaProjects/teamAuth/frontend/src/.umi", "regenerator-runtime": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\regenerator-runtime", "antd": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\antd", "moment": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\dayjs", "@umijs/max": "@@/exports"}, "requestRecord": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}, "hash": true, "routes": [{"path": "/user", "layout": false, "routes": [{"name": "login", "path": "/user/login", "component": "./user/login"}]}, {"path": "/invite/:token", "name": "团队邀请", "component": "./invite/[token]", "layout": false}, {"path": "/dashboard", "name": "仪表盘", "icon": "dashboard", "component": "./Dashboard"}, {"path": "/personal-center", "name": "个人中心", "icon": "user", "component": "./personal-center", "layout": false}, {"path": "/settings", "name": "设置", "icon": "setting", "component": "./settings", "layout": false}, {"path": "/help", "name": "帮助中心", "icon": "question", "component": "./help", "hideInMenu": true}, {"path": "/", "redirect": "/dashboard"}, {"path": "*", "layout": false, "component": "./404"}], "proxy": {"/api/": {"target": "http://localhost:8080", "changeOrigin": true, "pathRewrite": {"^/api/": "/api/v1/"}}}, "fastRefresh": true, "model": {}, "initialState": {}, "title": "团队协作管理系统", "layout": {"locale": false, "navTheme": "light", "colorPrimary": "#1890ff", "layout": "side", "contentWidth": "Fluid", "fixedHeader": false, "fixSiderbar": true, "colorWeak": false, "title": "团队协作管理系统", "pwa": false, "logo": "/logo.svg", "iconfontUrl": "", "token": {}}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "antd": {"appConfig": {}, "configProvider": {"theme": {"cssVar": true, "token": {"fontFamily": "AlibabaSans, sans-serif"}}}}, "request": {}, "access": {}, "headScripts": [{"src": "\\scripts\\loading.js", "async": true}], "presets": ["umi-presets-pro"], "mako": {"plugins": [{"name": "UmiHtmlGenerationMako"}]}, "esbuildMinifyIIFE": true, "exportStatic": {}, "targets": {"chrome": 80}, "hmrGuardian": false, "theme": {"blue-base": "#1890ff", "blue-1": "#e6f7ff", "blue-2": "#bae7ff", "blue-3": "#91d5ff", "blue-4": "#69c0ff", "blue-5": "#40a9ff", "blue-6": "#1890ff", "blue-7": "#096dd9", "blue-8": "#0050b3", "blue-9": "#003a8c", "blue-10": "#002766", "purple-base": "#722ed1", "purple-1": "#f9f0ff", "purple-2": "#efdbff", "purple-3": "#d3adf7", "purple-4": "#b37feb", "purple-5": "#9254de", "purple-6": "#722ed1", "purple-7": "#531dab", "purple-8": "#391085", "purple-9": "#22075e", "purple-10": "#120338", "cyan-base": "#13c2c2", "cyan-1": "#e6fffb", "cyan-2": "#b5f5ec", "cyan-3": "#87e8de", "cyan-4": "#5cdbd3", "cyan-5": "#36cfc9", "cyan-6": "#13c2c2", "cyan-7": "#08979c", "cyan-8": "#006d75", "cyan-9": "#00474f", "cyan-10": "#002329", "green-base": "#52c41a", "green-1": "#f6ffed", "green-2": "#d9f7be", "green-3": "#b7eb8f", "green-4": "#95de64", "green-5": "#73d13d", "green-6": "#52c41a", "green-7": "#389e0d", "green-8": "#237804", "green-9": "#135200", "green-10": "#092b00", "magenta-base": "#eb2f96", "magenta-1": "#fff0f6", "magenta-2": "#ffd6e7", "magenta-3": "#ffadd2", "magenta-4": "#ff85c0", "magenta-5": "#f759ab", "magenta-6": "#eb2f96", "magenta-7": "#c41d7f", "magenta-8": "#9e1068", "magenta-9": "#780650", "magenta-10": "#520339", "pink-base": "#eb2f96", "pink-1": "#fff0f6", "pink-2": "#ffd6e7", "pink-3": "#ffadd2", "pink-4": "#ff85c0", "pink-5": "#f759ab", "pink-6": "#eb2f96", "pink-7": "#c41d7f", "pink-8": "#9e1068", "pink-9": "#780650", "pink-10": "#520339", "red-base": "#f5222d", "red-1": "#fff1f0", "red-2": "#ffccc7", "red-3": "#ffa39e", "red-4": "#ff7875", "red-5": "#ff4d4f", "red-6": "#f5222d", "red-7": "#cf1322", "red-8": "#a8071a", "red-9": "#820014", "red-10": "#5c0011", "orange-base": "#fa8c16", "orange-1": "#fff7e6", "orange-2": "#ffe7ba", "orange-3": "#ffd591", "orange-4": "#ffc069", "orange-5": "#ffa940", "orange-6": "#fa8c16", "orange-7": "#d46b08", "orange-8": "#ad4e00", "orange-9": "#873800", "orange-10": "#612500", "yellow-base": "#fadb14", "yellow-1": "#feffe6", "yellow-2": "#ffffb8", "yellow-3": "#fffb8f", "yellow-4": "#fff566", "yellow-5": "#ffec3d", "yellow-6": "#fadb14", "yellow-7": "#d4b106", "yellow-8": "#ad8b00", "yellow-9": "#876800", "yellow-10": "#614700", "volcano-base": "#fa541c", "volcano-1": "#fff2e8", "volcano-2": "#ffd8bf", "volcano-3": "#ffbb96", "volcano-4": "#ff9c6e", "volcano-5": "#ff7a45", "volcano-6": "#fa541c", "volcano-7": "#d4380d", "volcano-8": "#ad2102", "volcano-9": "#871400", "volcano-10": "#610b00", "geekblue-base": "#2f54eb", "geekblue-1": "#f0f5ff", "geekblue-2": "#d6e4ff", "geekblue-3": "#adc6ff", "geekblue-4": "#85a5ff", "geekblue-5": "#597ef7", "geekblue-6": "#2f54eb", "geekblue-7": "#1d39c4", "geekblue-8": "#10239e", "geekblue-9": "#061178", "geekblue-10": "#030852", "lime-base": "#a0d911", "lime-1": "#fcffe6", "lime-2": "#f4ffb8", "lime-3": "#eaff8f", "lime-4": "#d3f261", "lime-5": "#bae637", "lime-6": "#a0d911", "lime-7": "#7cb305", "lime-8": "#5b8c00", "lime-9": "#3f6600", "lime-10": "#254000", "gold-base": "#faad14", "gold-1": "#fffbe6", "gold-2": "#fff1b8", "gold-3": "#ffe58f", "gold-4": "#ffd666", "gold-5": "#ffc53d", "gold-6": "#faad14", "gold-7": "#d48806", "gold-8": "#ad6800", "gold-9": "#874d00", "gold-10": "#613400", "preset-colors": "pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,", "theme": "default", "ant-prefix": "ant", "html-selector": "html", "primary-color": "#1890ff", "primary-color-hover": "#40a9ff", "primary-color-active": "#096dd9", "primary-color-outline": "rgba(24, 144, 255, 0.2)", "processing-color": "#1890ff", "info-color": "#1890ff", "info-color-deprecated-bg": "#e6f7ff", "info-color-deprecated-border": "#91d5ff", "success-color": "#52c41a", "success-color-hover": "#73d13d", "success-color-active": "#389e0d", "success-color-outline": "rgba(82, 196, 26, 0.2)", "success-color-deprecated-bg": "#f6ffed", "success-color-deprecated-border": "#b7eb8f", "warning-color": "#faad14", "warning-color-hover": "#ffc53d", "warning-color-active": "#d48806", "warning-color-outline": "rgba(250, 173, 20, 0.2)", "warning-color-deprecated-bg": "#fffbe6", "warning-color-deprecated-border": "#ffe58f", "error-color": "#ff4d4f", "error-color-hover": "#ff7875", "error-color-active": "#d9363e", "error-color-outline": "rgba(255, 77, 79, 0.2)", "error-color-deprecated-bg": "#fff2f0", "error-color-deprecated-border": "#ffccc7", "highlight-color": "#ff4d4f", "normal-color": "#d9d9d9", "white": "#fff", "black": "#000", "primary-1": "#e6f7ff", "primary-2": "#bae7ff", "primary-3": "#91d5ff", "primary-4": "#69c0ff", "primary-5": "#40a9ff", "primary-6": "#1890ff", "primary-7": "#096dd9", "primary-8": "#0050b3", "primary-9": "#003a8c", "primary-10": "#002766", "component-background": "#fff", "popover-background": "#fff", "popover-customize-border-color": "#f0f0f0", "font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "code-family": "'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace", "text-color": "rgba(0, 0, 0, 0.85)", "text-color-secondary": "rgba(0, 0, 0, 0.45)", "text-color-inverse": "#fff", "icon-color": "inherit", "icon-color-hover": "rgba(0, 0, 0, 0.75)", "heading-color": "rgba(0, 0, 0, 0.85)", "text-color-dark": "rgba(255, 255, 255, 0.85)", "text-color-secondary-dark": "rgba(255, 255, 255, 0.65)", "text-selection-bg": "#1890ff", "font-variant-base": "tabular-nums", "font-feature-settings-base": "tnum", "font-size-base": "14px", "font-size-lg": "16px", "font-size-sm": "12px", "heading-1-size": "38px", "heading-2-size": "30px", "heading-3-size": "24px", "heading-4-size": "20px", "heading-5-size": "16px", "line-height-base": "1.5715", "border-radius-base": "2px", "border-radius-sm": "2px", "control-border-radius": "2px", "arrow-border-radius": "2px", "padding-lg": "24px", "padding-md": "16px", "padding-sm": "12px", "padding-xs": "8px", "padding-xss": "4px", "control-padding-horizontal": "12px", "control-padding-horizontal-sm": "8px", "margin-lg": "24px", "margin-md": "16px", "margin-sm": "12px", "margin-xs": "8px", "margin-xss": "4px", "height-base": "32px", "height-lg": "40px", "height-sm": "24px", "item-active-bg": "#e6f7ff", "item-hover-bg": "#f5f5f5", "iconfont-css-prefix": "anticon", "link-color": "#1890ff", "link-hover-color": "#40a9ff", "link-active-color": "#096dd9", "link-decoration": "none", "link-hover-decoration": "none", "link-focus-decoration": "none", "link-focus-outline": "0", "ease-base-out": "cubic-bezier(0.7, 0.3, 0.1, 1)", "ease-base-in": "cubic-bezier(0.9, 0, 0.3, 0.7)", "ease-out": "cubic-bezier(0.215, 0.61, 0.355, 1)", "ease-in": "cubic-bezier(0.55, 0.055, 0.675, 0.19)", "ease-in-out": "cubic-bezier(0.645, 0.045, 0.355, 1)", "ease-out-back": "cubic-bezier(0.12, 0.4, 0.29, 1.46)", "ease-in-back": "cubic-bezier(0.71, -0.46, 0.88, 0.6)", "ease-in-out-back": "cubic-bezier(0.71, -0.46, 0.29, 1.46)", "ease-out-circ": "cubic-bezier(0.08, 0.82, 0.17, 1)", "ease-in-circ": "cubic-bezier(0.6, 0.04, 0.98, 0.34)", "ease-in-out-circ": "cubic-bezier(0.78, 0.14, 0.15, 0.86)", "ease-out-quint": "cubic-bezier(0.23, 1, 0.32, 1)", "ease-in-quint": "cubic-bezier(0.755, 0.05, 0.855, 0.06)", "ease-in-out-quint": "cubic-bezier(0.86, 0, 0.07, 1)", "border-color-base": "#d9d9d9", "border-color-split": "#f0f0f0", "border-color-inverse": "#fff", "border-width-base": "1px", "border-style-base": "solid", "outline-blur-size": "0", "outline-width": "2px", "outline-color": "#1890ff", "outline-fade": "20%", "background-color-light": "#fafafa", "background-color-base": "#f5f5f5", "disabled-color": "rgba(0, 0, 0, 0.25)", "disabled-bg": "#f5f5f5", "disabled-active-bg": "#e6e6e6", "disabled-color-dark": "rgba(255, 255, 255, 0.35)", "shadow-color": "rgba(0, 0, 0, 0.15)", "shadow-color-inverse": "#fff", "box-shadow-base": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "shadow-1-up": "0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-down": "0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-left": "-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-right": "6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-2": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "btn-font-weight": "400", "btn-border-radius-base": "2px", "btn-border-radius-sm": "2px", "btn-border-width": "1px", "btn-border-style": "solid", "btn-shadow": "0 2px 0 rgba(0, 0, 0, 0.015)", "btn-primary-shadow": "0 2px 0 rgba(0, 0, 0, 0.045)", "btn-text-shadow": "0 -1px 0 rgba(0, 0, 0, 0.12)", "btn-primary-color": "#fff", "btn-primary-bg": "#1890ff", "btn-default-color": "rgba(0, 0, 0, 0.85)", "btn-default-bg": "#fff", "btn-default-border": "#d9d9d9", "btn-danger-color": "#fff", "btn-danger-bg": "#ff4d4f", "btn-danger-border": "#ff4d4f", "btn-disable-color": "rgba(0, 0, 0, 0.25)", "btn-disable-bg": "#f5f5f5", "btn-disable-border": "#d9d9d9", "btn-default-ghost-color": "#fff", "btn-default-ghost-bg": "transparent", "btn-default-ghost-border": "#fff", "btn-font-size-lg": "16px", "btn-font-size-sm": "14px", "btn-padding-horizontal-base": "15px", "btn-padding-horizontal-lg": "15px", "btn-padding-horizontal-sm": "7px", "btn-height-base": "32px", "btn-height-lg": "40px", "btn-height-sm": "24px", "btn-line-height": "1.5715", "btn-circle-size": "32px", "btn-circle-size-lg": "40px", "btn-circle-size-sm": "24px", "btn-square-size": "32px", "btn-square-size-lg": "40px", "btn-square-size-sm": "24px", "btn-square-only-icon-size": "16px", "btn-square-only-icon-size-sm": "14px", "btn-square-only-icon-size-lg": "18px", "btn-group-border": "#40a9ff", "btn-link-hover-bg": "transparent", "btn-text-hover-bg": "rgba(0, 0, 0, 0.018)", "checkbox-size": "16px", "checkbox-color": "#1890ff", "checkbox-check-color": "#fff", "checkbox-check-bg": "#fff", "checkbox-border-width": "1px", "checkbox-border-radius": "2px", "checkbox-group-item-margin-right": "8px", "descriptions-bg": "#fafafa", "descriptions-title-margin-bottom": "20px", "descriptions-default-padding": "16px 24px", "descriptions-middle-padding": "12px 24px", "descriptions-small-padding": "8px 16px", "descriptions-item-padding-bottom": "16px", "descriptions-item-trailing-colon": "true", "descriptions-item-label-colon-margin-right": "8px", "descriptions-item-label-colon-margin-left": "2px", "descriptions-extra-color": "rgba(0, 0, 0, 0.85)", "divider-text-padding": "1em", "divider-orientation-margin": "5%", "divider-color": "rgba(0, 0, 0, 0.06)", "divider-vertical-gutter": "8px", "dropdown-selected-color": "#1890ff", "dropdown-menu-submenu-disabled-bg": "#fff", "dropdown-selected-bg": "#e6f7ff", "empty-font-size": "14px", "radio-size": "16px", "radio-top": "0.2em", "radio-border-width": "1px", "radio-dot-size": "8px", "radio-dot-color": "#1890ff", "radio-dot-disabled-color": "rgba(0, 0, 0, 0.2)", "radio-solid-checked-color": "#fff", "radio-button-bg": "#fff", "radio-button-checked-bg": "#fff", "radio-button-color": "rgba(0, 0, 0, 0.85)", "radio-button-hover-color": "#40a9ff", "radio-button-active-color": "#096dd9", "radio-button-padding-horizontal": "15px", "radio-disabled-button-checked-bg": "#e6e6e6", "radio-disabled-button-checked-color": "rgba(0, 0, 0, 0.25)", "radio-wrapper-margin-right": "8px", "screen-xs": "480px", "screen-xs-min": "480px", "screen-sm": "576px", "screen-sm-min": "576px", "screen-md": "768px", "screen-md-min": "768px", "screen-lg": "992px", "screen-lg-min": "992px", "screen-xl": "1200px", "screen-xl-min": "1200px", "screen-xxl": "1600px", "screen-xxl-min": "1600px", "screen-xs-max": "575px", "screen-sm-max": "767px", "screen-md-max": "991px", "screen-lg-max": "1199px", "screen-xl-max": "1599px", "grid-columns": "24", "layout-header-background": "#001529", "layout-header-height": "64px", "layout-header-padding": "0 50px", "layout-header-color": "rgba(0, 0, 0, 0.85)", "layout-footer-padding": "24px 50px", "layout-footer-background": "#f0f2f5", "layout-sider-background": "#001529", "layout-trigger-height": "48px", "layout-trigger-background": "#002140", "layout-trigger-color": "#fff", "layout-zero-trigger-width": "36px", "layout-zero-trigger-height": "42px", "layout-sider-background-light": "#fff", "layout-trigger-background-light": "#fff", "layout-trigger-color-light": "rgba(0, 0, 0, 0.85)", "zindex-badge": "auto", "zindex-table-fixed": "2", "zindex-affix": "10", "zindex-back-top": "10", "zindex-picker-panel": "10", "zindex-popup-close": "10", "zindex-modal": "1000", "zindex-modal-mask": "1000", "zindex-message": "1010", "zindex-notification": "1010", "zindex-popover": "1030", "zindex-dropdown": "1050", "zindex-picker": "1050", "zindex-popoconfirm": "1060", "zindex-tooltip": "1070", "zindex-image": "1080", "animation-duration-slow": "0.3s", "animation-duration-base": "0.2s", "animation-duration-fast": "0.1s", "collapse-panel-border-radius": "2px", "dropdown-menu-bg": "#fff", "dropdown-vertical-padding": "5px", "dropdown-edge-child-vertical-padding": "4px", "dropdown-font-size": "14px", "dropdown-line-height": "22px", "label-required-color": "#ff4d4f", "label-color": "rgba(0, 0, 0, 0.85)", "form-warning-input-bg": "#fff", "form-item-margin-bottom": "24px", "form-item-trailing-colon": "true", "form-vertical-label-padding": "0 0 8px", "form-vertical-label-margin": "0", "form-item-label-font-size": "14px", "form-item-label-height": "32px", "form-item-label-colon-margin-right": "8px", "form-item-label-colon-margin-left": "2px", "form-error-input-bg": "#fff", "input-height-base": "32px", "input-height-lg": "40px", "input-height-sm": "24px", "input-padding-horizontal": "11px", "input-padding-horizontal-base": "11px", "input-padding-horizontal-sm": "7px", "input-padding-horizontal-lg": "11px", "input-padding-vertical-base": "4px", "input-padding-vertical-sm": "0px", "input-padding-vertical-lg": "6.5px", "input-placeholder-color": "#bfbfbf", "input-color": "rgba(0, 0, 0, 0.85)", "input-icon-color": "rgba(0, 0, 0, 0.85)", "input-border-color": "#d9d9d9", "input-bg": "#fff", "input-number-hover-border-color": "#40a9ff", "input-number-handler-active-bg": "#f4f4f4", "input-number-handler-hover-bg": "#40a9ff", "input-number-handler-bg": "#fff", "input-number-handler-border-color": "#d9d9d9", "input-addon-bg": "#fafafa", "input-hover-border-color": "#40a9ff", "input-disabled-bg": "#f5f5f5", "input-outline-offset": "0 0", "input-icon-hover-color": "rgba(0, 0, 0, 0.85)", "input-disabled-color": "rgba(0, 0, 0, 0.25)", "mentions-dropdown-bg": "#fff", "mentions-dropdown-menu-item-hover-bg": "#fff", "select-border-color": "#d9d9d9", "select-item-selected-color": "rgba(0, 0, 0, 0.85)", "select-item-selected-font-weight": "600", "select-dropdown-bg": "#fff", "select-item-selected-bg": "#e6f7ff", "select-item-active-bg": "#f5f5f5", "select-dropdown-vertical-padding": "5px", "select-dropdown-font-size": "14px", "select-dropdown-line-height": "22px", "select-dropdown-height": "32px", "select-background": "#fff", "select-clear-background": "#fff", "select-selection-item-bg": "#f5f5f5", "select-selection-item-border-color": "#f0f0f0", "select-single-item-height-lg": "40px", "select-multiple-item-height": "24px", "select-multiple-item-height-lg": "32px", "select-multiple-item-spacing-half": "2px", "select-multiple-disabled-background": "#f5f5f5", "select-multiple-item-disabled-color": "#bfbfbf", "select-multiple-item-disabled-border-color": "#d9d9d9", "cascader-bg": "#fff", "cascader-item-selected-bg": "#e6f7ff", "cascader-menu-bg": "#fff", "cascader-menu-border-color-split": "#f0f0f0", "cascader-dropdown-vertical-padding": "5px", "cascader-dropdown-edge-child-vertical-padding": "4px", "cascader-dropdown-font-size": "14px", "cascader-dropdown-line-height": "22px", "anchor-bg": "transparent", "anchor-border-color": "#f0f0f0", "anchor-link-top": "4px", "anchor-link-left": "16px", "anchor-link-padding": "4px 0 4px 16px", "tooltip-max-width": "250px", "tooltip-color": "#fff", "tooltip-bg": "rgba(0, 0, 0, 0.75)", "tooltip-arrow-width": "11.3137085px", "tooltip-distance": "14.3137085px", "tooltip-arrow-color": "rgba(0, 0, 0, 0.75)", "tooltip-border-radius": "2px", "popover-bg": "#fff", "popover-color": "rgba(0, 0, 0, 0.85)", "popover-min-width": "177px", "popover-min-height": "32px", "popover-arrow-width": "11.3137085px", "popover-arrow-color": "#fff", "popover-arrow-outer-color": "#fff", "popover-distance": "15.3137085px", "popover-padding-horizontal": "16px", "modal-header-padding-vertical": "16px", "modal-header-padding-horizontal": "24px", "modal-header-bg": "#fff", "modal-header-padding": "16px 24px", "modal-header-border-width": "1px", "modal-header-border-style": "solid", "modal-header-title-line-height": "22px", "modal-header-title-font-size": "16px", "modal-header-border-color-split": "#f0f0f0", "modal-header-close-size": "54px", "modal-content-bg": "#fff", "modal-heading-color": "rgba(0, 0, 0, 0.85)", "modal-close-color": "rgba(0, 0, 0, 0.45)", "modal-footer-bg": "transparent", "modal-footer-border-color-split": "#f0f0f0", "modal-footer-border-style": "solid", "modal-footer-padding-vertical": "10px", "modal-footer-padding-horizontal": "16px", "modal-footer-border-width": "1px", "modal-mask-bg": "rgba(0, 0, 0, 0.45)", "modal-confirm-title-font-size": "16px", "modal-border-radius": "2px", "progress-default-color": "#1890ff", "progress-remaining-color": "#f5f5f5", "progress-info-text-color": "rgba(0, 0, 0, 0.85)", "progress-radius": "100px", "progress-steps-item-bg": "#f3f3f3", "progress-text-font-size": "1em", "progress-text-color": "rgba(0, 0, 0, 0.85)", "progress-circle-text-font-size": "1em", "menu-inline-toplevel-item-height": "40px", "menu-item-height": "40px", "menu-item-group-height": "1.5715", "menu-collapsed-width": "80px", "menu-bg": "#fff", "menu-popup-bg": "#fff", "menu-item-color": "rgba(0, 0, 0, 0.85)", "menu-inline-submenu-bg": "#fafafa", "menu-highlight-color": "#1890ff", "menu-highlight-danger-color": "#ff4d4f", "menu-item-active-bg": "#e6f7ff", "menu-item-active-danger-bg": "#fff1f0", "menu-item-active-border-width": "3px", "menu-item-group-title-color": "rgba(0, 0, 0, 0.45)", "menu-item-vertical-margin": "4px", "menu-item-font-size": "14px", "menu-item-boundary-margin": "8px", "menu-item-padding-horizontal": "20px", "menu-item-padding": "0 20px", "menu-horizontal-line-height": "46px", "menu-icon-margin-right": "10px", "menu-icon-size": "14px", "menu-icon-size-lg": "16px", "menu-item-group-title-font-size": "14px", "menu-dark-color": "rgba(255, 255, 255, 0.65)", "menu-dark-danger-color": "#ff4d4f", "menu-dark-bg": "#001529", "menu-dark-arrow-color": "#fff", "menu-dark-inline-submenu-bg": "#000c17", "menu-dark-highlight-color": "#fff", "menu-dark-item-active-bg": "#1890ff", "menu-dark-item-active-danger-bg": "#ff4d4f", "menu-dark-selected-item-icon-color": "#fff", "menu-dark-selected-item-text-color": "#fff", "menu-dark-item-hover-bg": "transparent", "spin-dot-size-sm": "14px", "spin-dot-size": "20px", "spin-dot-size-lg": "32px", "table-bg": "#fff", "table-header-bg": "#fafafa", "table-header-color": "rgba(0, 0, 0, 0.85)", "table-header-sort-bg": "#f5f5f5", "table-row-hover-bg": "#fafafa", "table-selected-row-color": "inherit", "table-selected-row-bg": "#e6f7ff", "table-selected-row-hover-bg": "#dcf4ff", "table-expanded-row-bg": "#fbfbfb", "table-padding-vertical": "16px", "table-padding-horizontal": "16px", "table-padding-vertical-md": "12px", "table-padding-horizontal-md": "8px", "table-padding-vertical-sm": "8px", "table-padding-horizontal-sm": "8px", "table-border-color": "#f0f0f0", "table-border-radius-base": "2px", "table-footer-bg": "#fafafa", "table-footer-color": "rgba(0, 0, 0, 0.85)", "table-header-bg-sm": "#fafafa", "table-font-size": "14px", "table-font-size-md": "14px", "table-font-size-sm": "14px", "table-header-cell-split-color": "rgba(0, 0, 0, 0.06)", "table-header-sort-active-bg": "rgba(0, 0, 0, 0.04)", "table-fixed-header-sort-active-bg": "#f5f5f5", "table-header-filter-active-bg": "rgba(0, 0, 0, 0.04)", "table-filter-btns-bg": "inherit", "table-filter-dropdown-bg": "#fff", "table-expand-icon-bg": "#fff", "table-selection-column-width": "32px", "table-sticky-scroll-bar-bg": "rgba(0, 0, 0, 0.35)", "table-sticky-scroll-bar-radius": "4px", "tag-border-radius": "2px", "tag-default-bg": "#fafafa", "tag-default-color": "rgba(0, 0, 0, 0.85)", "tag-font-size": "12px", "tag-line-height": "20px", "picker-bg": "#fff", "picker-basic-cell-hover-color": "#f5f5f5", "picker-basic-cell-active-with-range-color": "#e6f7ff", "picker-basic-cell-hover-with-range-color": "#cbe6ff", "picker-basic-cell-disabled-bg": "rgba(0, 0, 0, 0.04)", "picker-border-color": "#f0f0f0", "picker-date-hover-range-border-color": "#7ec1ff", "picker-date-hover-range-color": "#cbe6ff", "picker-time-panel-column-width": "56px", "picker-time-panel-column-height": "224px", "picker-time-panel-cell-height": "28px", "picker-panel-cell-height": "24px", "picker-panel-cell-width": "36px", "picker-text-height": "40px", "picker-panel-without-time-cell-height": "66px", "calendar-bg": "#fff", "calendar-input-bg": "#fff", "calendar-border-color": "#fff", "calendar-item-active-bg": "#e6f7ff", "calendar-column-active-bg": "rgba(230, 247, 255, 0.2)", "calendar-full-bg": "#fff", "calendar-full-panel-bg": "#fff", "carousel-dot-width": "16px", "carousel-dot-height": "3px", "carousel-dot-active-width": "24px", "badge-height": "20px", "badge-height-sm": "14px", "badge-dot-size": "6px", "badge-font-size": "12px", "badge-font-size-sm": "12px", "badge-font-weight": "normal", "badge-status-size": "6px", "badge-text-color": "#fff", "badge-color": "#ff4d4f", "rate-star-color": "#fadb14", "rate-star-bg": "#f0f0f0", "rate-star-size": "20px", "rate-star-hover-scale": "scale(1.1)", "card-head-color": "rgba(0, 0, 0, 0.85)", "card-head-background": "transparent", "card-head-font-size": "16px", "card-head-font-size-sm": "14px", "card-head-padding": "16px", "card-head-padding-sm": "8px", "card-head-height": "48px", "card-head-height-sm": "36px", "card-inner-head-padding": "12px", "card-padding-base": "24px", "card-padding-base-sm": "12px", "card-actions-background": "#fff", "card-actions-li-margin": "12px 0", "card-skeleton-bg": "#cfd8dc", "card-background": "#fff", "card-shadow": "0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)", "card-radius": "2px", "card-head-tabs-margin-bottom": "-17px", "card-head-extra-color": "rgba(0, 0, 0, 0.85)", "comment-bg": "inherit", "comment-padding-base": "16px 0", "comment-nest-indent": "44px", "comment-font-size-base": "14px", "comment-font-size-sm": "12px", "comment-author-name-color": "rgba(0, 0, 0, 0.45)", "comment-author-time-color": "#ccc", "comment-action-color": "rgba(0, 0, 0, 0.45)", "comment-action-hover-color": "#595959", "comment-actions-margin-bottom": "inherit", "comment-actions-margin-top": "12px", "comment-content-detail-p-margin-bottom": "inherit", "tabs-card-head-background": "#fafafa", "tabs-card-height": "40px", "tabs-card-active-color": "#1890ff", "tabs-card-horizontal-padding": "8px 16px", "tabs-card-horizontal-padding-sm": "6px 16px", "tabs-card-horizontal-padding-lg": "7px 16px 6px", "tabs-title-font-size": "14px", "tabs-title-font-size-lg": "16px", "tabs-title-font-size-sm": "14px", "tabs-ink-bar-color": "#1890ff", "tabs-bar-margin": "0 0 16px 0", "tabs-horizontal-gutter": "32px", "tabs-horizontal-margin": "0 0 0 32px", "tabs-horizontal-margin-rtl": "0 0 0 32px", "tabs-horizontal-padding": "12px 0", "tabs-horizontal-padding-lg": "16px 0", "tabs-horizontal-padding-sm": "8px 0", "tabs-vertical-padding": "8px 24px", "tabs-vertical-margin": "16px 0 0 0", "tabs-scrolling-size": "32px", "tabs-highlight-color": "#1890ff", "tabs-hover-color": "#40a9ff", "tabs-active-color": "#096dd9", "tabs-card-gutter": "2px", "tabs-card-tab-active-border-top": "2px solid transparent", "back-top-color": "#fff", "back-top-bg": "rgba(0, 0, 0, 0.45)", "back-top-hover-bg": "rgba(0, 0, 0, 0.85)", "avatar-size-base": "32px", "avatar-size-lg": "40px", "avatar-size-sm": "24px", "avatar-font-size-base": "18px", "avatar-font-size-lg": "24px", "avatar-font-size-sm": "14px", "avatar-bg": "#ccc", "avatar-color": "#fff", "avatar-border-radius": "2px", "avatar-group-overlapping": "-8px", "avatar-group-space": "3px", "avatar-group-border-color": "#fff", "switch-height": "22px", "switch-sm-height": "16px", "switch-min-width": "44px", "switch-sm-min-width": "28px", "switch-disabled-opacity": "0.4", "switch-color": "#1890ff", "switch-bg": "#fff", "switch-shadow-color": "rgba(0, 35, 11, 0.2)", "switch-padding": "2px", "switch-inner-margin-min": "7px", "switch-inner-margin-max": "25px", "switch-sm-inner-margin-min": "5px", "switch-sm-inner-margin-max": "18px", "pagination-item-bg": "#fff", "pagination-item-size": "32px", "pagination-item-size-sm": "24px", "pagination-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "pagination-font-weight-active": "500", "pagination-item-bg-active": "#fff", "pagination-item-link-bg": "#fff", "pagination-item-disabled-color-active": "rgba(0, 0, 0, 0.25)", "pagination-item-disabled-bg-active": "#e6e6e6", "pagination-item-input-bg": "#fff", "pagination-mini-options-size-changer-top": "0px", "page-header-padding": "24px", "page-header-padding-vertical": "16px", "page-header-padding-breadcrumb": "12px", "page-header-content-padding-vertical": "12px", "page-header-back-color": "#000", "page-header-ghost-bg": "inherit", "page-header-heading-title": "20px", "page-header-heading-sub-title": "14px", "page-header-tabs-tab-font-size": "16px", "breadcrumb-base-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-last-item-color": "rgba(0, 0, 0, 0.85)", "breadcrumb-font-size": "14px", "breadcrumb-icon-font-size": "14px", "breadcrumb-link-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-link-color-hover": "rgba(0, 0, 0, 0.85)", "breadcrumb-separator-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-separator-margin": "0 8px", "slider-margin": "10px 6px 10px", "slider-rail-background-color": "#f5f5f5", "slider-rail-background-color-hover": "#e1e1e1", "slider-track-background-color": "#91d5ff", "slider-track-background-color-hover": "#69c0ff", "slider-handle-border-width": "2px", "slider-handle-background-color": "#fff", "slider-handle-color": "#91d5ff", "slider-handle-color-hover": "#69c0ff", "slider-handle-color-focus": "#46a6ff", "slider-handle-color-focus-shadow": "rgba(24, 144, 255, 0.12)", "slider-handle-color-tooltip-open": "#1890ff", "slider-handle-size": "14px", "slider-handle-margin-top": "-5px", "slider-handle-shadow": "0", "slider-dot-border-color": "#f0f0f0", "slider-dot-border-color-active": "#8cc8ff", "slider-disabled-color": "rgba(0, 0, 0, 0.25)", "slider-disabled-background-color": "#fff", "tree-bg": "#fff", "tree-title-height": "24px", "tree-child-padding": "18px", "tree-directory-selected-color": "#fff", "tree-directory-selected-bg": "#1890ff", "tree-node-hover-bg": "#f5f5f5", "tree-node-selected-bg": "#bae7ff", "collapse-header-padding": "12px 16px", "collapse-header-padding-extra": "40px", "collapse-header-bg": "#fafafa", "collapse-content-padding": "16px", "collapse-content-bg": "#fff", "collapse-header-arrow-left": "16px", "skeleton-color": "rgba(190, 190, 190, 0.2)", "skeleton-to-color": "rgba(129, 129, 129, 0.24)", "skeleton-paragraph-margin-top": "28px", "skeleton-paragraph-li-margin-top": "16px", "skeleton-paragraph-li-height": "16px", "skeleton-title-height": "16px", "skeleton-title-paragraph-margin-top": "24px", "transfer-header-height": "40px", "transfer-item-height": "32px", "transfer-disabled-bg": "#f5f5f5", "transfer-list-height": "200px", "transfer-item-hover-bg": "#f5f5f5", "transfer-item-selected-hover-bg": "#dcf4ff", "transfer-item-padding-vertical": "6px", "transfer-list-search-icon-top": "12px", "message-notice-content-padding": "10px 16px", "message-notice-content-bg": "#fff", "wave-animation-width": "6px", "alert-success-border-color": "#b7eb8f", "alert-success-bg-color": "#f6ffed", "alert-success-icon-color": "#52c41a", "alert-info-border-color": "#91d5ff", "alert-info-bg-color": "#e6f7ff", "alert-info-icon-color": "#1890ff", "alert-warning-border-color": "#ffe58f", "alert-warning-bg-color": "#fffbe6", "alert-warning-icon-color": "#faad14", "alert-error-border-color": "#ffccc7", "alert-error-bg-color": "#fff2f0", "alert-error-icon-color": "#ff4d4f", "alert-message-color": "rgba(0, 0, 0, 0.85)", "alert-text-color": "rgba(0, 0, 0, 0.85)", "alert-close-color": "rgba(0, 0, 0, 0.45)", "alert-close-hover-color": "rgba(0, 0, 0, 0.75)", "alert-no-icon-padding-vertical": "8px", "alert-with-description-no-icon-padding-vertical": "15px", "alert-with-description-padding-vertical": "15px", "alert-with-description-padding": "15px 15px 15px 24px", "alert-icon-top": "12.0005px", "alert-with-description-icon-size": "24px", "list-header-background": "transparent", "list-footer-background": "transparent", "list-empty-text-padding": "16px", "list-item-padding": "12px 0", "list-item-padding-sm": "8px 16px", "list-item-padding-lg": "16px 24px", "list-item-meta-margin-bottom": "16px", "list-item-meta-avatar-margin-right": "16px", "list-item-meta-title-margin-bottom": "12px", "list-customize-card-bg": "#fff", "list-item-meta-description-font-size": "14px", "statistic-title-font-size": "14px", "statistic-content-font-size": "24px", "statistic-unit-font-size": "24px", "statistic-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "drawer-header-padding": "16px 24px", "drawer-bg": "#fff", "drawer-footer-padding-vertical": "10px", "drawer-footer-padding-horizontal": "16px", "drawer-header-close-size": "56px", "drawer-title-font-size": "16px", "drawer-title-line-height": "22px", "timeline-width": "2px", "timeline-color": "#f0f0f0", "timeline-dot-border-width": "2px", "timeline-dot-color": "#1890ff", "timeline-dot-bg": "#fff", "timeline-item-padding-bottom": "20px", "typography-title-font-weight": "600", "typography-title-margin-top": "1.2em", "typography-title-margin-bottom": "0.5em", "upload-actions-color": "rgba(0, 0, 0, 0.45)", "process-tail-color": "#f0f0f0", "steps-nav-arrow-color": "rgba(0, 0, 0, 0.25)", "steps-background": "#fff", "steps-icon-size": "32px", "steps-icon-custom-size": "32px", "steps-icon-custom-top": "0px", "steps-icon-custom-font-size": "24px", "steps-icon-top": "-0.5px", "steps-icon-font-size": "16px", "steps-icon-margin": "0 8px 0 0", "steps-title-line-height": "32px", "steps-small-icon-size": "24px", "steps-small-icon-margin": "0 8px 0 0", "steps-dot-size": "8px", "steps-dot-top": "2px", "steps-current-dot-size": "10px", "steps-description-max-width": "140px", "steps-nav-content-max-width": "auto", "steps-vertical-icon-width": "16px", "steps-vertical-tail-width": "16px", "steps-vertical-tail-width-sm": "12px", "notification-bg": "#fff", "notification-padding-vertical": "16px", "notification-padding-horizontal": "24px", "result-title-font-size": "24px", "result-subtitle-font-size": "14px", "result-icon-font-size": "72px", "result-extra-margin": "24px 0 0 0", "image-size-base": "48px", "image-font-size-base": "24px", "image-bg": "#f5f5f5", "image-color": "#fff", "image-mask-font-size": "16px", "image-preview-operation-size": "18px", "image-preview-operation-color": "rgba(255, 255, 255, 0.85)", "image-preview-operation-disabled-color": "rgba(255, 255, 255, 0.25)", "segmented-bg": "rgba(0, 0, 0, 0.04)", "segmented-hover-bg": "rgba(0, 0, 0, 0.06)", "segmented-selected-bg": "#fff", "segmented-label-color": "rgba(0, 0, 0, 0.65)", "segmented-label-hover-color": "#262626"}, "define": {"ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION": "", "REACT_APP_ENV": "dev"}}, "routes": {"1": {"path": "/user", "layout": false, "id": "1", "absPath": "/user"}, "2": {"name": "login", "path": "/user/login", "file": "@/pages/user/login/index.tsx", "parentId": "1", "id": "2", "absPath": "/user/login", "__content": "/**\n * 登录页面\n * 实现双阶段认证的第一阶段：账号登录\n */\n\nimport { MailOutlined, SafetyOutlined } from '@ant-design/icons';\nimport { Helmet, history, useModel } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Form,\n  Input,\n  message,\n  Space,\n  Typography,\n} from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState, useCallback, useMemo, useEffect } from 'react';\nimport { Footer } from '@/components';\nimport { AuthService } from '@/services';\nimport type { LoginRequest, SendVerificationCodeRequest } from '@/types/api';\nimport Settings from '../../../../config/defaultSettings';\n\nconst { Title, Text } = Typography;\n\n// 登录表单组件（移到外部避免重新创建）\nconst LoginFormComponent: React.FC<{\n  form: any;\n  handleLogin: (values: LoginRequest) => void;\n  handleSendCode: () => void;\n  sendingCode: boolean;\n  countdown: number;\n  loading: boolean;\n}> = React.memo(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading }) => {\n  // 使用 useMemo 稳定按钮渲染，避免因倒计时变化导致输入框重新渲染\n  const sendCodeButton = useMemo(() => (\n    <Button\n      type=\"link\"\n      size=\"small\"\n      disabled={countdown > 0 || sendingCode}\n      loading={sendingCode}\n      onClick={handleSendCode}\n      style={{ padding: 0, height: 'auto' }}\n    >\n      {countdown > 0 ? `${countdown}s后重发` : '发送验证码'}\n    </Button>\n  ), [countdown, sendingCode, handleSendCode]);\n\n  // 使用 useMemo 稳定邮箱输入框，避免重新渲染\n  const emailField = useMemo(() => (\n    <Form.Item\n      key=\"email-field\"\n      name=\"email\"\n      rules={[\n        { required: true, message: '请输入邮箱！' },\n        { type: 'email', message: '请输入有效的邮箱地址！' },\n      ]}\n    >\n      <Input\n        key=\"email-input\"\n        prefix={<MailOutlined />}\n        placeholder=\"邮箱\"\n        autoComplete=\"email\"\n      />\n    </Form.Item>\n  ), []);\n\n  // 使用 useMemo 稳定验证码输入框，只在按钮变化时重新渲染\n  const codeField = useMemo(() => (\n    <Form.Item\n      key=\"code-field\"\n      name=\"code\"\n      rules={[\n        { required: true, message: '请输入验证码！' },\n        { len: 6, message: '验证码为6位数字！' },\n        { pattern: /^\\d{6}$/, message: '验证码只能包含数字！' },\n      ]}\n    >\n      <Input\n        key=\"code-input\"\n        prefix={<SafetyOutlined />}\n        placeholder=\"6位验证码\"\n        maxLength={6}\n        suffix={sendCodeButton}\n      />\n    </Form.Item>\n  ), [sendCodeButton]);\n\n  return (\n    <Form\n      form={form}\n      name=\"login\"\n      size=\"large\"\n      onFinish={handleLogin}\n      autoComplete=\"off\"\n    >\n      {emailField}\n      {codeField}\n\n      {/* 提示信息 */}\n      <div style={{ marginBottom: 16, textAlign: 'center' }}>\n        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n          新用户将自动完成注册并登录\n        </Text>\n      </div>\n\n      <Form.Item>\n        <Button type=\"primary\" htmlType=\"submit\" loading={loading} block>\n          登录 / 注册\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n});\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    loginCard: {\n      width: '100%',\n      maxWidth: 400,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n    lang: {\n      width: 42,\n      height: 42,\n      lineHeight: '42px',\n      position: 'fixed',\n      right: 16,\n      top: 16,\n      borderRadius: token.borderRadius,\n      ':hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n  };\n});\n\nconst LoginPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [sendingCode, setSendingCode] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [form] = Form.useForm(); // 将表单实例提升到父组件\n  const { styles } = useStyles();\n  const { setInitialState } = useModel('@@initialState');\n\n  // 使用 Form 内置的邮箱验证\n\n  // 组件挂载时清除倒计时状态，避免页面刷新后无法输入\n  useEffect(() => {\n    setCountdown(0);\n  }, []);\n\n  // 倒计时效果\n  React.useEffect(() => {\n    let timer: NodeJS.Timeout;\n    if (countdown > 0) {\n      timer = setTimeout(() => {\n        setCountdown(countdown - 1);\n      }, 1000);\n    }\n    return () => {\n      if (timer) clearTimeout(timer);\n    };\n  }, [countdown]);\n\n  // 发送验证码\n  const handleSendCode = useCallback(async (type: 'login' | 'register' = 'login') => {\n    let email: string;\n\n    try {\n      // 验证邮箱字段\n      await form.validateFields(['email']);\n\n      // 从表单获取邮箱值\n      email = form.getFieldValue('email');\n\n      if (!email) {\n        return;\n      }\n    } catch (error) {\n      // 表单验证失败，由表单验证规则处理错误显示\n      return;\n    }\n\n    setSendingCode(true);\n    try {\n      const request: SendVerificationCodeRequest = { email, type };\n      const response = await AuthService.sendVerificationCode(request);\n\n      if (response.success) {\n        setCountdown(60); // 60秒倒计时\n      } else {\n        if (response.nextSendTime) {\n          setCountdown(response.nextSendTime);\n        }\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    } finally {\n      setSendingCode(false);\n    }\n  }, [form]);\n\n  // 处理登录/注册\n  const handleLogin = useCallback(async (values: LoginRequest) => {\n    setLoading(true);\n    try {\n      const response = await AuthService.login(values);\n\n      // 登录成功后停止倒计时\n      setCountdown(0);\n\n      // 登录成功后，刷新 initialState\n      await setInitialState((prevState) => ({\n        ...prevState,\n        currentUser: response.user,\n        currentTeam: response.teams.length > 0 ? response.teams[0] : undefined,\n      }));\n\n      // 根据团队数量进行不同的跳转处理\n      if (response.teams.length === 0) {\n        // 没有团队，跳转到个人中心页面\n        history.push('/personal-center');\n      } else {\n        // 有团队（无论一个还是多个），都跳转到个人中心整合页面\n        history.push('/personal-center', { teams: response.teams });\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    } finally {\n      setLoading(false);\n    }\n  }, [setInitialState]);\n\n  // 注册功能已移除，统一使用验证码登录/注册流程\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          登录 / 注册\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队管理系统</Title>\n              <Text type=\"secondary\">现代化的团队协作与管理平台</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.loginCard}>\n          <LoginFormComponent\n            form={form}\n            handleLogin={handleLogin}\n            handleSendCode={() => handleSendCode('login')}\n            sendingCode={sendingCode}\n            countdown={countdown}\n            loading={loading}\n          />\n        </Card>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default LoginPage;\n", "__isJSFile": true, "__absFile": "H:/projects/IdeaProjects/teamAuth/frontend/src/pages/user/login/index.tsx"}, "3": {"path": "/invite/:token", "name": "团队邀请", "layout": false, "file": "@/pages/invite/[token].tsx", "id": "3", "absPath": "/invite/:token", "__content": "/**\n * 邀请链接处理页面\n * 路由: /invite/:token\n */\n\nimport { TeamOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';\nimport { Helmet, history, useParams } from '@umijs/max';\nimport {\n  Button,\n  Space,\n  Typography,\n  Result,\n  Spin,\n  Alert,\n  Divider,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport { createStyles } from 'antd-style';\nimport React, { useState, useEffect } from 'react';\nimport { Footer } from '@/components';\nimport { InvitationService } from '@/services';\nimport type { InvitationInfoResponse } from '@/types/api';\nimport { TokenManager } from '@/utils/request';\nimport Settings from '../../../config/defaultSettings';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    inviteCard: {\n      width: '100%',\n      maxWidth: 500,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n  };\n});\n\nconst InvitePage: React.FC = () => {\n  const { token } = useParams<{ token: string }>();\n  const [loading, setLoading] = useState(true);\n  const [processing, setProcessing] = useState(false);\n  const [invitationInfo, setInvitationInfo] = useState<InvitationInfoResponse | null>(null);\n  const [result, setResult] = useState<any>(null);\n  const { styles } = useStyles();\n\n  // 获取邀请信息\n  const fetchInvitationInfo = async () => {\n    if (!token) {\n      setResult({\n        type: 'error',\n        title: '邀请链接无效',\n        message: '邀请链接格式错误或已过期',\n      });\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const response = await InvitationService.getInvitationInfo(token);\n\n      if (response.success) {\n        setInvitationInfo(response);\n      } else {\n        setResult({\n          type: 'error',\n          title: '邀请链接无效',\n          message: response.errorMessage || '无法获取邀请信息',\n        });\n      }\n    } catch (error) {\n      setResult({\n        type: 'error',\n        title: '获取邀请信息失败',\n        message: '网络错误，请稍后重试',\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理邀请接受\n  const handleAcceptInvitation = async () => {\n    if (!token) return;\n\n    setProcessing(true);\n    try {\n      const response = await InvitationService.acceptInvitationByLink(token, {});\n\n      if (response.success) {\n        // 如果是新用户且返回了访问令牌，自动登录\n        if (response.isNewUser && response.accessToken) {\n          try {\n            TokenManager.setToken(response.accessToken);\n\n\n            setResult({\n              type: 'success',\n              title: '欢迎加入团队！',\n              message: `您的账号已自动创建并登录（无需密码）。${response.nextAction || '正在跳转到个人中心...'}`,\n              teamName: response.teamName,\n              isNewUser: response.isNewUser,\n              autoLogin: true,\n            });\n\n            // 延迟跳转到个人中心\n            setTimeout(() => {\n              history.push('/personal-center');\n            }, 3000);\n          } catch (error) {\n\n            setResult({\n              type: 'success',\n              title: '账号创建成功！',\n              message: '您的账号已成功创建（无需密码），请使用邮箱验证码登录后查看团队信息。',\n              teamName: response.teamName,\n              isNewUser: response.isNewUser,\n              autoLogin: false,\n            });\n          }\n        } else if (response.isNewUser) {\n          // 新用户但没有自动登录令牌\n          setResult({\n            type: 'success',\n            title: '账号创建成功！',\n            message: '您的账号已成功创建（无需密码），请使用邮箱验证码登录后查看团队信息。',\n            teamName: response.teamName,\n            isNewUser: response.isNewUser,\n            autoLogin: false,\n          });\n        } else {\n          // 现有用户\n          setResult({\n            type: 'success',\n            title: '加入成功！',\n            message: response.nextAction || '您已成功加入团队，请登录后查看团队信息。',\n            teamName: response.teamName,\n            isNewUser: response.isNewUser,\n            autoLogin: false,\n          });\n        }\n      } else {\n        setResult({\n          type: 'error',\n          title: '加入失败',\n          message: response.errorMessage || '处理邀请时发生错误',\n        });\n      }\n    } catch (error) {\n      setResult({\n        type: 'error',\n        title: '加入失败',\n        message: '网络错误，请稍后重试',\n      });\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // 处理取消操作\n  const handleCancel = () => {\n    history.push('/');\n  };\n\n  // 邀请确认界面\n  const InvitationConfirm = () => {\n    if (!invitationInfo) return null;\n\n    return (\n      <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n        <TeamOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 24 }} />\n        <Title level={3}>团队邀请</Title>\n\n        <div style={{ marginBottom: 32 }}>\n          <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n            <div>\n              <Text type=\"secondary\">您被邀请加入团队：</Text>\n              <br />\n              <Title level={4} style={{ margin: '8px 0', color: '#1890ff' }}>\n                {invitationInfo.teamName}\n              </Title>\n            </div>\n\n            {invitationInfo.inviterName && (\n              <div>\n                <Text type=\"secondary\">邀请人：</Text>\n                <Text strong>{invitationInfo.inviterName}</Text>\n              </div>\n            )}\n\n            {invitationInfo.message && (\n              <div>\n                <Text type=\"secondary\">邀请消息：</Text>\n                <br />\n                <Text italic>\"{invitationInfo.message}\"</Text>\n              </div>\n            )}\n          </Space>\n        </div>\n\n        <Divider />\n\n        <div style={{ marginTop: 24 }}>\n          <Title level={4} style={{ marginBottom: 24 }}>\n            您确定要加入此团队吗？\n          </Title>\n\n          <Space size=\"large\">\n            <Button\n              type=\"primary\"\n              size=\"large\"\n              loading={processing}\n              onClick={handleAcceptInvitation}\n              icon={<CheckCircleOutlined />}\n              disabled={invitationInfo.isExpired}\n            >\n              {processing ? '正在处理...' : '确认加入'}\n            </Button>\n            <Button\n              size=\"large\"\n              onClick={handleCancel}\n              icon={<CloseCircleOutlined />}\n              disabled={processing}\n            >\n              取消\n            </Button>\n          </Space>\n        </div>\n\n        {invitationInfo.isExpired && (\n          <Alert\n            message=\"邀请已过期\"\n            description=\"此邀请链接已过期，请联系团队管理员重新发送邀请。\"\n            type=\"warning\"\n            showIcon\n            style={{ marginTop: 24 }}\n          />\n        )}\n      </div>\n    );\n  };\n\n  // 结果展示\n  const ResultDisplay = () => {\n    if (!result) return null;\n\n    // 根据结果类型和自动登录状态显示不同的按钮\n    const getExtraButtons = () => {\n      if (result.type === 'success') {\n        if (result.autoLogin) {\n          // 自动登录成功，显示跳转到个人中心的按钮\n          return [\n            <Button type=\"primary\" key=\"personal-center\" onClick={() => history.push('/personal-center')}>\n              前往个人中心\n            </Button>,\n          ];\n        } else if (result.isNewUser) {\n          // 新用户但未自动登录，引导去登录\n          return [\n            <Button type=\"primary\" key=\"login\" onClick={() => history.push('/user/login')}>\n              前往登录\n            </Button>,\n            <Button key=\"home\" onClick={() => history.push('/')}>\n              返回首页\n            </Button>,\n          ];\n        } else {\n          // 现有用户，引导去登录\n          return [\n            <Button type=\"primary\" key=\"login\" onClick={() => history.push('/user/login')}>\n              前往登录\n            </Button>,\n            <Button key=\"home\" onClick={() => history.push('/')}>\n              返回首页\n            </Button>,\n          ];\n        }\n      } else {\n        // 错误情况，显示重试和返回首页\n        return [\n          <Button type=\"primary\" key=\"retry\" onClick={() => window.location.reload()}>\n            重试\n          </Button>,\n          <Button key=\"home\" onClick={() => history.push('/')}>\n            返回首页\n          </Button>,\n        ];\n      }\n    };\n\n    return (\n      <Result\n        status={result.type}\n        title={result.title}\n        subTitle={result.message}\n        extra={getExtraButtons()}\n      />\n    );\n  };\n\n  // 页面加载时获取邀请信息\n  useEffect(() => {\n    fetchInvitationInfo();\n  }, [token]);\n\n  // 加载中状态\n  if (loading) {\n    return (\n      <div className={styles.container}>\n        <Helmet>\n          <title>\n            团队邀请\n            {Settings.title && ` - ${Settings.title}`}\n          </title>\n        </Helmet>\n        <div className={styles.content}>\n          <ProCard className={styles.inviteCard}>\n            <div style={{ textAlign: 'center', padding: '60px 20px' }}>\n              <Spin size=\"large\" />\n              <div style={{ marginTop: 16 }}>\n                <Text type=\"secondary\">正在加载邀请信息...</Text>\n              </div>\n            </div>\n          </ProCard>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  // 显示结果页面\n  if (result) {\n    return (\n      <div className={styles.container}>\n        <Helmet>\n          <title>\n            团队邀请\n            {Settings.title && ` - ${Settings.title}`}\n          </title>\n        </Helmet>\n        <div className={styles.content}>\n          <ProCard className={styles.inviteCard}>\n            <ResultDisplay />\n          </ProCard>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  // 显示邀请确认页面\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          团队邀请\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队邀请</Title>\n              <Text type=\"secondary\">加入团队，开始协作</Text>\n            </div>\n          </Space>\n        </div>\n\n        <ProCard className={styles.inviteCard}>\n          <InvitationConfirm />\n        </ProCard>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default InvitePage;\n", "__isJSFile": true, "__absFile": "H:/projects/IdeaProjects/teamAuth/frontend/src/pages/invite/[token].tsx"}, "4": {"path": "/dashboard", "name": "仪表盘", "icon": "dashboard", "file": "@/pages/Dashboard/index.tsx", "parentId": "ant-design-pro-layout", "id": "4", "absPath": "/dashboard", "__content": "/**\n * 仪表板页面\n */\n\nimport { PageContainer } from '@ant-design/pro-components';\nimport React from 'react';\n\nconst Dashboard: React.FC = () => {\n  return (\n    <PageContainer title=\"仪表板\">\n      <div style={{\n        height: '400px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: '#999',\n        fontSize: '16px'\n      }}>\n        {/* 空白页面 */}\n      </div>\n    </PageContainer>\n  );\n};\n\nexport default Dashboard;\n", "__isJSFile": true, "__absFile": "H:/projects/IdeaProjects/teamAuth/frontend/src/pages/Dashboard/index.tsx"}, "5": {"path": "/personal-center", "name": "个人中心", "icon": "user", "layout": false, "file": "@/pages/personal-center/index.tsx", "id": "5", "absPath": "/personal-center", "__content": "import { useModel } from '@umijs/max';\nimport { Col, Row, Spin } from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamListCard from './TeamListCard';\nimport TodoManagement from './TodoManagement';\nimport PersonalInfo from './PersonalInfo';\n\n/**\n * 个人中心页面组件\n *\n * 这是用户的个人中心主页面，提供用户信息管理、团队管理、待办事项等功能。\n * 是用户进行个人设置和团队操作的主要入口页面。\n *\n * 页面功能：\n * 1. 用户个人信息展示和编辑\n * 2. 团队列表显示和团队切换\n * 3. 个人待办事项管理\n * 4. 全局浮动操作按钮\n *\n * 页面结构：\n * - 左列：个人信息、数据概览、上次登录信息、团队列表（响应式布局）\n * - 右列：待办事项管理（响应式布局）\n * - 浮动：全局操作按钮\n *\n * 权限控制：\n * - 需要用户登录才能访问\n * - 自动检查登录状态并重定向\n * - 支持登录状态变化的实时响应\n *\n * 响应式设计：\n * - 移动端：垂直堆叠布局\n * - 桌面端：左右分栏布局\n * - 自适应不同屏幕尺寸\n */\nconst PersonalCenterPage: React.FC = () => {\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户信息和加载状态：\n   * - initialState: 包含用户和团队信息的全局状态\n   * - loading: 全局状态的加载状态\n   */\n  const { initialState, loading } = useModel('@@initialState');\n\n\n\n  /**\n   * 加载状态处理\n   *\n   * 当全局状态正在初始化时，显示加载界面。\n   * 这确保了用户在状态加载完成前看到友好的加载提示。\n   */\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>\n      </div>\n    );\n  }\n\n  /**\n   * 登录状态检查已由应用级路由守卫处理\n   *\n   * 移除页面级别的登录检查，避免与应用级路由守卫冲突。\n   * 应用级路由守卫在 app.tsx 中的 onPageChange 函数已经处理了\n   * 用户登录状态检查和重定向逻辑，无需在页面组件中重复检查。\n   *\n   * 这样可以避免登录成功后的状态更新时序问题，确保用户\n   * 一次登录成功后能够正常访问个人中心页面。\n   */\n\n  return (\n    <>\n      {/* 页面主容器 */}\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff', // 浅蓝色背景，营造清新的视觉效果\n          padding: '12px 12px 24px 12px', // 移动端优化：减少左右边距，增加底部边距\n        }}\n      >\n        {/*\n         * 主内容卡片容器\n         *\n         * 使用Card组件作为主要内容的容器，提供：\n         * 1. 统一的视觉边界和阴影效果\n         * 2. 响应式的内边距设置\n         * 3. 圆角设计提升视觉体验\n         * 4. 全高度布局适配不同屏幕\n         */}\n        <ProCard\n          style={{\n            width: '100%',\n            minHeight: 'calc(100vh - 48px)', // 减去外层padding的高度\n            borderRadius: '12px', // 圆角设计\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)', // 轻微阴影效果\n          }}\n          bodyStyle={{\n            padding: '24px', // 内容区域的内边距\n          }}\n        >\n          {/*\n           * 响应式两列布局\n           *\n           * 使用Ant Design的Row/Col组件实现响应式两列布局：\n           * - 移动端：垂直堆叠，所有组件占满宽度\n           * - 桌面端：左列包含个人信息、数据概览、登录信息、团队列表；右列包含待办事项\n           * - gutter: 组件间距设置\n           * - margin: 0: 避免Row组件的默认负边距影响布局\n           */}\n          <Row gutter={[24, 16]} style={{ margin: 0 }}>\n            {/*\n             * 左列：个人信息区域\n             *\n             * 包含以下组件（按顺序）：\n             * 1. 个人信息部分（用户详情、头像、姓名、联系方式、登录信息等）\n             * 2. 数据概览部分（统计信息、指标或摘要数据）\n             * 3. 团队列表部分（用户所属团队列表）\n             *\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据左半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n            >\n              {/* 个人信息部分（包含用户详情、姓名、联系方式、登录信息和数据概览） */}\n              <PersonalInfo />\n\n              {/* 团队列表部分（用户所属团队列表） */}\n              <TeamListCard />\n            </Col>\n\n            {/*\n             * 右列：待办事项管理区域\n             *\n             * 个人待办事项的管理界面，支持添加、编辑、删除待办事项。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据右半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n            >\n              <TodoManagement />\n            </Col>\n          </Row>\n        </ProCard>\n      </div>\n\n      {/*\n       * 全局浮动操作按钮\n       *\n       * 提供快速访问常用功能的浮动按钮，如：\n       * - 快速创建团队\n       * - 用户设置\n       * - 帮助信息\n       *\n       * 位置固定在页面右下角，不受页面滚动影响。\n       */}\n      <UserFloatButton />\n\n\n\n\n    </>\n  );\n};\n\nexport default PersonalCenterPage;\n", "__isJSFile": true, "__absFile": "H:/projects/IdeaProjects/teamAuth/frontend/src/pages/personal-center/index.tsx"}, "6": {"path": "/settings", "name": "设置", "icon": "setting", "layout": false, "file": "@/pages/settings/index.tsx", "id": "6", "absPath": "/settings", "__content": "import { useModel, history } from '@umijs/max';\nimport { Col, Row, Spin, Typography, Divider, Button, Space } from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport { ArrowLeftOutlined } from '@ant-design/icons';\nimport React, { useEffect } from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamManagementCard from './TeamManagementCard';\nimport UserProfileCard from './UserProfileCard';\n\nconst { Title, Text } = Typography;\n\n/**\n * 设置页面组件\n *\n * 这是用户的设置主页面，提供用户个人设置、团队管理等功能。\n * 是用户进行各种配置和管理操作的主要入口页面。\n *\n * 页面功能：\n * 1. 用户个人信息设置\n * 2. 团队创建和管理\n * 3. 账户设置和偏好配置\n * 4. 订阅和计费管理\n *\n * 页面结构：\n * - 顶部：页面标题和描述\n * - 左侧：用户个人设置（响应式布局）\n * - 右侧：团队管理设置（响应式布局）\n * - 浮动：全局操作按钮\n *\n * 权限控制：\n * - 需要用户登录才能访问\n * - 由应用级路由守卫处理登录检查\n * - 支持登录状态变化的实时响应\n *\n * 响应式设计：\n * - 移动端：垂直堆叠布局\n * - 桌面端：左右分栏布局\n * - 自适应不同屏幕尺寸\n */\nconst SettingsPage: React.FC = () => {\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户信息和加载状态：\n   * - initialState: 包含用户和团队信息的全局状态\n   * - loading: 全局状态的加载状态\n   */\n  const { initialState, loading } = useModel('@@initialState');\n\n  /**\n   * 返回上一页\n   *\n   * 在UmiJS 4中，history.goBack()方法已被移除，使用history.go(-1)替代。\n   * 如果没有历史记录可以返回，则跳转到仪表盘作为默认页面。\n   */\n  const handleGoBack = () => {\n    // 检查是否有历史记录可以返回\n    if (window.history.length > 1) {\n      history.go(-1);\n    } else {\n      // 如果没有历史记录，跳转到仪表盘\n      history.push('/dashboard');\n    }\n  };\n\n  /**\n   * 加载状态处理\n   *\n   * 当全局状态正在初始化时，显示加载界面。\n   * 这确保了用户在状态加载完成前看到友好的加载提示。\n   */\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载设置信息...</div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {/* 页面主容器 */}\n      <div\n        style={{\n          minHeight: '100vh',\n          background: 'linear-gradient(135deg, #f5f8ff 0%, #e8f4fd 100%)',\n          padding: '24px',\n        }}\n      >\n        {/* 页面标题区域 */}\n        <div style={{ marginBottom: 24 }}>\n          {/* 返回按钮 */}\n          <div style={{ marginBottom: 16 }}>\n            <Button\n              type=\"text\"\n              icon={<ArrowLeftOutlined />}\n              onClick={handleGoBack}\n              style={{\n                fontSize: '16px',\n                color: '#1890ff',\n                padding: '4px 8px',\n              }}\n            >\n              返回\n            </Button>\n          </div>\n\n          {/* 标题 */}\n          <div style={{ textAlign: 'center' }}>\n            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n              设置中心\n            </Title>\n            <Text type=\"secondary\" style={{ fontSize: '16px' }}>\n              管理您的个人信息、团队设置和账户偏好\n            </Text>\n          </div>\n        </div>\n\n        <ProCard\n          style={{\n            maxWidth: 1400,\n            margin: '0 auto',\n            borderRadius: 16,\n            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',\n            border: 'none',\n            background: 'rgba(255, 255, 255, 0.95)',\n            backdropFilter: 'blur(10px)',\n          }}\n          bodyStyle={{\n            padding: '32px',\n          }}\n        >\n          {/*\n           * 响应式网格布局\n           *\n           * 使用Ant Design的Row/Col组件实现响应式布局：\n           * - 移动端：垂直堆叠，所有组件占满宽度\n           * - 桌面端：个人设置和团队管理左右分栏\n           * - gutter: 组件间距设置\n           * - margin: 0: 避免Row组件的默认负边距影响布局\n           */}\n          <Row gutter={[24, 24]} style={{ margin: 0 }}>\n            {/*\n             * 用户个人设置区域\n             *\n             * 显示用户的个人信息设置、账户配置等。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据左半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n              style={{ marginBottom: 8 }}\n            >\n              <UserProfileCard />\n            </Col>\n\n            {/*\n             * 团队管理设置区域\n             *\n             * 显示团队创建、团队管理等功能。\n             * 这是团队管理功能的主要入口。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据右半部分\n             */}\n            <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>\n              <TeamManagementCard />\n            </Col>\n          </Row>\n        </ProCard>\n      </div>\n\n      {/* 全局浮动操作按钮 */}\n      <UserFloatButton />\n    </>\n  );\n};\n\nexport default SettingsPage;\n", "__isJSFile": true, "__absFile": "H:/projects/IdeaProjects/teamAuth/frontend/src/pages/settings/index.tsx"}, "7": {"path": "/help", "name": "帮助中心", "icon": "question", "hideInMenu": true, "file": "@/pages/help/index.tsx", "parentId": "ant-design-pro-layout", "id": "7", "absPath": "/help", "__content": "import {\n  BookOutlined,\n  QuestionCircleOutlined,\n  SettingOutlined,\n  TeamOutlined,\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Button, Divider, Space, Typography } from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\n\nconst { Title, Paragraph, Text } = Typography;\n\nconst HelpPage: React.FC = () => {\n  return (\n    <PageContainer\n      title=\"帮助中心\"\n      subTitle=\"团队协作管理系统使用指南\"\n      extra={[\n        <Button key=\"contact\" type=\"primary\">\n          联系技术支持\n        </Button>,\n      ]}\n    >\n      <div style={{ maxWidth: 1200, margin: '0 auto' }}>\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          {/* 快速开始 */}\n          <ProCard>\n            <Title level={3}>\n              <BookOutlined style={{ marginRight: 8 }} />\n              快速开始\n            </Title>\n            <Paragraph>\n              欢迎使用团队协作管理系统！本系统帮助您高效管理团队成员、项目和任务。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>首次使用步骤：</Text>\n              <ol>\n                <li>注册账号并登录系统</li>\n                <li>创建或加入团队</li>\n                <li>邀请团队成员</li>\n                <li>开始协作管理</li>\n              </ol>\n            </Paragraph>\n          </ProCard>\n\n          {/* 团队管理 */}\n          <ProCard>\n            <Title level={3}>\n              <TeamOutlined style={{ marginRight: 8 }} />\n              团队管理\n            </Title>\n            <Paragraph>\n              <Text strong>创建团队：</Text>\n              在团队页面点击\"创建团队\"按钮，填写团队信息即可创建新团队。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>邀请成员：</Text>\n              团队管理员可以通过邮箱邀请新成员加入团队。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>角色权限：</Text>\n              系统支持多种角色权限，包括管理员、普通成员等，确保团队协作的安全性。\n            </Paragraph>\n          </ProCard>\n\n          {/* 系统设置 */}\n          <ProCard>\n            <Title level={3}>\n              <SettingOutlined style={{ marginRight: 8 }} />\n              系统设置\n            </Title>\n            <Paragraph>\n              <Text strong>个人设置：</Text>\n              在右上角头像菜单中可以修改个人信息、密码等设置。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>团队设置：</Text>\n              团队管理员可以在团队设置页面修改团队信息、管理成员权限。\n            </Paragraph>\n          </ProCard>\n\n          {/* 常见问题 */}\n          <ProCard>\n            <Title level={3}>\n              <QuestionCircleOutlined style={{ marginRight: 8 }} />\n              常见问题\n            </Title>\n            <Paragraph>\n              <Text strong>Q: 如何切换团队？</Text>\n              <br />\n              A: 在顶部导航栏的团队名称处点击，可以选择切换到其他团队。\n            </Paragraph>\n            <Divider />\n            <Paragraph>\n              <Text strong>Q: 忘记密码怎么办？</Text>\n              <br />\n              A: 在登录页面点击\"忘记密码\"，通过邮箱重置密码。\n            </Paragraph>\n            <Divider />\n            <Paragraph>\n              <Text strong>Q: 如何邀请新成员？</Text>\n              <br />\n              A: 团队管理员可以在团队管理页面通过邮箱邀请新成员。\n            </Paragraph>\n          </ProCard>\n\n          {/* 联系我们 */}\n          <ProCard>\n            <Title level={3}>联系我们</Title>\n            <Paragraph>\n              如果您在使用过程中遇到问题，可以通过以下方式联系我们：\n            </Paragraph>\n            <Paragraph>\n              <Text strong>技术支持邮箱：</Text> <EMAIL>\n              <br />\n              <Text strong>用户反馈：</Text> <EMAIL>\n              <br />\n              <Text strong>工作时间：</Text> 周一至周五 9:00-18:00\n            </Paragraph>\n          </ProCard>\n        </Space>\n      </div>\n    </PageContainer>\n  );\n};\n\nexport default HelpPage;\n", "__isJSFile": true, "__absFile": "H:/projects/IdeaProjects/teamAuth/frontend/src/pages/help/index.tsx"}, "8": {"path": "/", "redirect": "/dashboard", "parentId": "ant-design-pro-layout", "id": "8", "absPath": "/"}, "9": {"path": "*", "layout": false, "file": "@/pages/404.tsx", "id": "9", "absPath": "/*", "__content": "import { history } from '@umijs/max';\nimport { Button, Result } from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React from 'react';\n\nconst NoFoundPage: React.FC = () => (\n  <ProCard bordered={false}>\n    <Result\n      status=\"404\"\n      title=\"404\"\n      subTitle=\"抱歉，您访问的页面不存在。\"\n      extra={\n        <Button type=\"primary\" onClick={() => history.push('/')}>\n          返回首页\n        </Button>\n      }\n    />\n  </ProCard>\n);\n\nexport default NoFoundPage;\n", "__isJSFile": true, "__absFile": "H:/projects/IdeaProjects/teamAuth/frontend/src/pages/404.tsx"}, "ant-design-pro-layout": {"id": "ant-design-pro-layout", "path": "/", "file": "H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/Layout.tsx", "absPath": "/", "isLayout": true, "__absFile": "H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/Layout.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "yarn", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "@umijs/max", "cliName": "max"}, "bundleStatus": {"done": false}, "react": {"version": "19.1.0", "path": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\react"}, "react-dom": {"version": "19.1.0", "path": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\react-dom"}, "appJS": {"path": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\app.tsx", "exports": ["getInitialState", "layout", "request"]}, "locale": "en-US", "globalCSS": ["H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\global.less"], "globalJS": ["H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\global.tsx"], "overridesCSS": [], "globalLoading": "H:/projects/IdeaProjects/teamAuth/frontend/src/loading.tsx", "bundler": "mako", "framework": "react", "typescript": {"tsVersion": "5.8.3", "tslibVersion": "2.8.1"}, "faviconFiles": [], "port": 8002, "host": "0.0.0.0", "ip": "************", "antd": {"pkgPath": "H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\antd", "version": "5.26.6"}, "pluginLayout": {"pkgPath": "H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@ant-design/pro-components", "version": "2.8.10"}}