{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.1512158356996877604.hot-update.js", "src/pages/personal-center/PersonalInfo.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='4961403529120130956';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  ClockCircleOutlined,\n  MailOutlined,\n  PhoneOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Button,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserProfileDetailResponse, UserPersonalStatsResponse } from '@/types/api';\nimport UnifiedSettingsModal from './UnifiedSettingsModal';\n\nconst { Title, Text } = Typography;\n\n/**\n * 个人信息组件\n *\n * 显示用户的完整个人信息，包括基本信息和登录历史。\n * 整合了原UserProfileCard和LastLoginInfo组件的功能。\n *\n * 主要功能：\n * 1. 显示用户头像（使用姓名首字母）\n * 2. 显示用户姓名、邮箱、电话\n * 3. 显示注册日期\n * 4. 显示最后登录时间和登录团队\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfileDetail()获取\n */\nconst PersonalInfo: React.FC = () => {\n  /**\n   * 用户详细信息状态管理\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // Modal状态管理\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n\n  // 获取用户数据\n  useEffect(() => {\n    const fetchUserData = async () => {\n      try {\n        const userDetail = await UserService.getUserProfileDetail();\n        setUserInfo(userDetail);\n        setUserInfoError(null);\n      } catch (error) {\n        console.error('获取用户详细信息失败:', error);\n        setUserInfoError('获取用户详细信息失败，请稍后重试');\n      } finally {\n        setUserInfoLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <ProCard\n      title=\"个人信息\"\n      extra={\n        <Button\n          size=\"small\"\n          icon={<SettingOutlined />}\n          onClick={() => setSettingsModalVisible(true)}\n          style={{\n            borderRadius: 6,\n            width: 28,\n            height: 28,\n            padding: 0,\n            border: '1px solid #d9d9d9',\n            color: '#595959',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n          }}\n        />\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n      }}\n      // styles={{\n      //   header: {\n      //     borderBottom: '1px solid #f0f0f0',\n      //     paddingBottom: 12,\n      //   },\n      //   body: {\n      //     padding: '16px',\n      //   },\n      // }}\n    >\n      {userInfoError ? (\n        <Alert\n          message=\"个人信息加载失败\"\n          description={userInfoError}\n          type=\"error\"\n          showIcon\n        />\n      ) : (\n        <Spin spinning={userInfoLoading}>\n          {/* 用户信息区域 */}\n          <div>\n                  {/* 第一行：姓名 */}\n                  <div style={{ marginBottom: '8px' }}>\n                    <Title\n                      level={4}\n                      style={{\n                        margin: 0,\n                        fontSize: 18,\n                        fontWeight: 600,\n                        color: '#262626',\n                        lineHeight: 1.3,\n                      }}\n                    >\n                      {userInfo.name || '加载中...'}\n                    </Title>\n                  </div>\n\n                  {/* 第二行：联系信息 */}\n                  <div style={{ marginBottom: '8px' }}>\n                    <Space size={16} wrap>\n                      {userInfo.email && (\n                        <Space size={6} align=\"center\">\n                          <MailOutlined\n                            style={{\n                              fontSize: 14,\n                              color: '#1890ff',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: '#595959',\n                              fontSize: 13,\n                              fontWeight: 500,\n                            }}\n                          >\n                            {userInfo.email}\n                          </Text>\n                        </Space>\n                      )}\n                      {userInfo.telephone && (\n                        <Space size={6} align=\"center\">\n                          <PhoneOutlined\n                            style={{\n                              fontSize: 14,\n                              color: '#52c41a',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: '#595959',\n                              fontSize: 13,\n                              fontWeight: 500,\n                            }}\n                          >\n                            {userInfo.telephone}\n                          </Text>\n                        </Space>\n                      )}\n                      {/* 注册日期 */}\n                      {userInfo.registerDate && (\n                        <Space size={4} align=\"center\">\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            📅 注册于 {userInfo.registerDate}\n                          </Text>\n                        </Space>\n                      )}\n                    </Space>\n                  </div>\n\n                  {/* 第三行：登录信息 */}\n                  <div>\n                    <Space size={16} wrap>\n                      {/* 最后登录时间 */}\n                      {userInfo.lastLoginTime && (\n                        <Space size={4} align=\"center\">\n                          <ClockCircleOutlined\n                            style={{\n                              fontSize: 12,\n                              color: '#1890ff',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            最后登录：{userInfo.lastLoginTime}\n                          </Text>\n                        </Space>\n                      )}\n\n                      {/* 最后登录团队 */}\n                      {userInfo.lastLoginTeam && (\n                        <Space size={4} align=\"center\">\n                          <TeamOutlined\n                            style={{\n                              fontSize: 12,\n                              color: '#52c41a',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            团队：{userInfo.lastLoginTeam}\n                          </Text>\n                        </Space>\n                      )}\n                    </Space>\n                  </div>\n                </div>\n        </Spin>\n      )}\n\n      {/* 统一设置Modal */}\n      <UnifiedSettingsModal\n        visible={settingsModalVisible}\n        onCancel={() => setSettingsModalVisible(false)}\n        userInfo={userInfo}\n        onSuccess={() => {\n          // 可以在这里刷新用户信息或团队列表\n          console.log('设置操作成功');\n        }}\n      />\n    </ProCard>\n  );\n};\n\nexport default PersonalInfo;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCmQb;;;2BAAA;;;;;;;0CA/PO;yCAOA;kDACiB;oFACmB;yCACf;kGAEK;;;;;;;;;;YAEjC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC;;;;;;;;;;;;;;CAcC,GACD,MAAM,eAAyB;;gBAC7B;;GAEC,GACD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;oBAClE,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;oBACX,QAAQ;gBACV;gBAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,YAAY;gBACZ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;gBAEjE,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,MAAM,aAAa,MAAM,iBAAW,CAAC,oBAAoB;4BACzD,YAAY;4BACZ,iBAAiB;wBACnB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,eAAe;4BAC7B,iBAAiB;wBACnB,SAAU;4BACR,mBAAmB;wBACrB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,sBAAO;oBACN,OAAM;oBACN,qBACE,2BAAC,YAAM;wBACL,MAAK;wBACL,oBAAM,2BAAC,sBAAe;;;;;wBACtB,SAAS,IAAM,wBAAwB;wBACvC,OAAO;4BACL,cAAc;4BACd,OAAO;4BACP,QAAQ;4BACR,SAAS;4BACT,QAAQ;4BACR,OAAO;4BACP,SAAS;4BACT,YAAY;4BACZ,gBAAgB;wBAClB;;;;;;oBAGJ,OAAO;wBACL,cAAc;wBACd,cAAc;oBAChB;;wBAWC,8BACC,2BAAC,WAAK;4BACJ,SAAQ;4BACR,aAAa;4BACb,MAAK;4BACL,QAAQ;;;;;iDAGV,2BAAC,UAAI;4BAAC,UAAU;sCAEd,cAAA,2BAAC;;kDAEO,2BAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAM;kDAChC,cAAA,2BAAC;4CACC,OAAO;4CACP,OAAO;gDACL,QAAQ;gDACR,UAAU;gDACV,YAAY;gDACZ,OAAO;gDACP,YAAY;4CACd;sDAEC,SAAS,IAAI,IAAI;;;;;;;;;;;kDAKtB,2BAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAM;kDAChC,cAAA,2BAAC,WAAK;4CAAC,MAAM;4CAAI,IAAI;;gDAClB,SAAS,KAAK,kBACb,2BAAC,WAAK;oDAAC,MAAM;oDAAG,OAAM;;sEACpB,2BAAC,mBAAY;4DACX,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC;4DACC,OAAO;gEACL,OAAO;gEACP,UAAU;gEACV,YAAY;4DACd;sEAEC,SAAS,KAAK;;;;;;;;;;;;gDAIpB,SAAS,SAAS,kBACjB,2BAAC,WAAK;oDAAC,MAAM;oDAAG,OAAM;;sEACpB,2BAAC,oBAAa;4DACZ,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC;4DACC,OAAO;gEACL,OAAO;gEACP,UAAU;gEACV,YAAY;4DACd;sEAEC,SAAS,SAAS;;;;;;;;;;;;gDAKxB,SAAS,YAAY,kBACpB,2BAAC,WAAK;oDAAC,MAAM;oDAAG,OAAM;8DACpB,cAAA,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,OAAO;4DACP,YAAY;wDACd;;4DACD;4DACS,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;kDAQvC,2BAAC;kDACC,cAAA,2BAAC,WAAK;4CAAC,MAAM;4CAAI,IAAI;;gDAElB,SAAS,aAAa,kBACrB,2BAAC,WAAK;oDAAC,MAAM;oDAAG,OAAM;;sEACpB,2BAAC,0BAAmB;4DAClB,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC;4DACC,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,YAAY;4DACd;;gEACD;gEACO,SAAS,aAAa;;;;;;;;;;;;;gDAMjC,SAAS,aAAa,kBACrB,2BAAC,WAAK;oDAAC,MAAM;oDAAG,OAAM;;sEACpB,2BAAC,mBAAY;4DACX,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC;4DACC,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,YAAY;4DACd;;gEACD;gEACK,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWhD,2BAAC,6BAAoB;4BACnB,SAAS;4BACT,UAAU,IAAM,wBAAwB;4BACxC,UAAU;4BACV,WAAW;gCACT,mBAAmB;gCACnB,QAAQ,GAAG,CAAC;4BACd;;;;;;;;;;;;YAIR;eA9NM;iBAAA;gBAgON,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDnQD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}