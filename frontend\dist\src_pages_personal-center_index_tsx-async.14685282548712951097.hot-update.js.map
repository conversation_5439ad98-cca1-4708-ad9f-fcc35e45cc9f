{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.14685282548712951097.hot-update.js", "src/pages/personal-center/TodoManagement.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='1512158356996877604';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  CalendarOutlined,\n  CheckOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  MoreOutlined,\n  PlusOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Button,\n  Col,\n  Dropdown,\n  Flex,\n  Form,\n  Input,\n  Modal,\n\n  Progress,\n  Row,\n  Select,\n  Space,\n  Spin,\n  Tabs,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport { ModalForm, ProList, ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { TodoService } from '@/services/todo';\nimport type { TodoResponse, TodoStatsResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { TabPane } = Tabs;\n\n// 使用API类型定义，不需要重复定义接口\ninterface TodoManagementProps {\n  onAddTodo?: (todo: TodoResponse) => void;\n  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;\n  onDeleteTodo?: (id: number) => void;\n}\n\nconst TodoManagement: React.FC<TodoManagementProps> = () => {\n  // TODO数据状态管理\n  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);\n  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({\n    highPriorityCount: 0,\n    mediumPriorityCount: 0,\n    lowPriorityCount: 0,\n    totalCount: 0,\n    completedCount: 0,\n    completionPercentage: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 待办事项状态管理\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\n  const [todoForm] = Form.useForm();\n  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);\n\n  // 过滤器状态\n  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'completed'>(\n    'pending',\n  );\n  const [searchText, setSearchText] = useState('');\n\n  // 获取TODO数据\n  useEffect(() => {\n    const fetchTodoData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        console.log('TodoManagement: 开始获取TODO数据');\n\n        // 分别获取TODO列表和统计数据，避免一个失败影响另一个\n        const todosPromise = TodoService.getUserTodos().catch((error) => {\n          console.error('获取TODO列表失败:', error);\n          return [];\n        });\n\n        const statsPromise = TodoService.getTodoStats().catch((error) => {\n          console.error('获取TODO统计失败:', error);\n          return {\n            highPriorityCount: 0,\n            mediumPriorityCount: 0,\n            lowPriorityCount: 0,\n            totalCount: 0,\n            completedCount: 0,\n            completionPercentage: 0,\n          };\n        });\n\n        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);\n\n        console.log('TodoManagement: 获取到TODO列表:', todos);\n        console.log('TodoManagement: 获取到统计数据:', stats);\n\n        setPersonalTasks(todos);\n        setTodoStats(stats);\n      } catch (error) {\n        console.error('获取TODO数据时发生未知错误:', error);\n        setError('获取TODO数据失败，请刷新页面重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTodoData();\n  }, []);\n\n  // 根据激活的标签和搜索文本过滤任务\n  const filteredPersonalTasks = (personalTasks || []).filter((task) => {\n    // 根据标签过滤\n    if (activeTab === 'pending' && task.status === 1) return false;\n    if (activeTab === 'completed' && task.status === 0) return false;\n\n    // 根据搜索文本过滤\n    if (\n      searchText &&\n      !task.title.toLowerCase().includes(searchText.toLowerCase())\n    ) {\n      return false;\n    }\n\n    return true;\n  });\n\n  // 处理待办事项操作\n  const handleToggleTodoStatus = async (id: number) => {\n    try {\n      const task = personalTasks.find((t) => t.id === id);\n      if (!task) {\n        return;\n      }\n\n      const newStatus = task.status === 0 ? 1 : 0;\n\n      await TodoService.updateTodo(id, { status: newStatus });\n\n      // 更新本地状态\n      setPersonalTasks(\n        personalTasks.map((task) =>\n          task.id === id ? { ...task, status: newStatus } : task,\n        ),\n      );\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleAddOrUpdateTodo = async (values: any) => {\n    try {\n      if (editingTodoId) {\n        // 更新现有待办事项\n        const updatedTodo = await TodoService.updateTodo(editingTodoId, {\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks(\n          personalTasks.map((task) =>\n            task.id === editingTodoId ? updatedTodo : task,\n          ),\n        );\n      } else {\n        // 添加新待办事项\n        const newTodo = await TodoService.createTodo({\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks([newTodo, ...personalTasks]);\n      }\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n\n      // 重置表单并关闭模态框\n      setTodoModalVisible(false);\n      setEditingTodoId(null);\n      todoForm.resetFields();\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleDeleteTodo = async (id: number) => {\n    try {\n      await TodoService.deleteTodo(id);\n      setPersonalTasks(personalTasks.filter((task) => task.id !== id));\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  return (\n    <ProCard\n      title=\"待办事项/任务列表\"\n      style={{\n        borderRadius: 8,\n        height: 'fit-content',\n        minHeight: '600px', // 确保右列有足够的高度\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '16px',\n      }}\n    >\n      {/* 响应式标题行：搜索框、新增按钮、优先级计数和完成率 */}\n      <div\n        style={{\n          marginBottom: 16,\n          padding: '12px 16px',\n          background: '#fafbfc',\n          borderRadius: 8,\n          border: '1px solid #f0f0f0',\n        }}\n      >\n        {/* 第一行：搜索框和新增按钮 */}\n        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n          <Col xs={24} sm={24} md={24} lg={24} xl={24}>\n            <Flex align=\"center\" gap={12} style={{ width: '100%' }}>\n              <Input.Search\n                placeholder=\"搜索任务...\"\n                allowClear\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ flex: 1 }}\n                size=\"middle\"\n              />\n\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => {\n                  setEditingTodoId(null);\n                  todoForm.resetFields();\n                  setTodoModalVisible(true);\n                }}\n                style={{\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                  fontWeight: 500,\n                  minWidth: 80,\n                }}\n                size=\"middle\"\n              >\n                新增\n              </Button>\n            </Flex>\n          </Col>\n        </Row>\n\n        {/* 第二行：完成率和优先级计数卡片 */}\n        <Row gutter={[12, 12]}>\n          <Col xs={24} sm={24} md={24} lg={24} xl={24}>\n            <Flex align=\"center\" justify=\"space-between\" wrap=\"wrap\" gap={12}>\n              {/* 完成率卡片 */}\n              <div\n                style={{\n                  background: '#f6ffed',\n                  border: '1px solid #b7eb8f',\n                  borderRadius: 8,\n                  padding: '10px 16px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 8,\n                  flex: '1 1 200px',\n                  minWidth: 200,\n                  maxWidth: 280,\n                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                }}\n              >\n                <Tooltip\n                  title={`完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`}\n                >\n                  <Flex align=\"center\" gap={8} style={{ width: '100%' }}>\n                    <Text\n                      style={{ fontSize: 13, fontWeight: 500, color: '#389e0d' }}\n                    >\n                      完成率\n                    </Text>\n                    <Progress\n                      percent={todoStats.completionPercentage}\n                      size=\"small\"\n                      style={{ flex: 1, minWidth: 80 }}\n                      strokeColor=\"#52c41a\"\n                      showInfo={false}\n                    />\n                    <Text\n                      style={{ fontSize: 13, fontWeight: 600, color: '#389e0d', minWidth: 35 }}\n                    >\n                      {todoStats.completionPercentage}%\n                    </Text>\n                  </Flex>\n                </Tooltip>\n              </div>\n\n              {/* 优先级统计卡片组 */}\n              <Flex align=\"center\" gap={8} wrap=\"wrap\">\n                {/* 高优先级卡片 */}\n                <div\n                  style={{\n                    background: '#fff2f0',\n                    border: '1px solid #ffccc7',\n                    borderRadius: 8,\n                    padding: '10px 14px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 8,\n                    flex: '1 1 auto',\n                    minWidth: 90,\n                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                  }}\n                >\n                  <Tooltip title={`高优先级任务: ${todoStats.highPriorityCount}个`}>\n                    <Flex align=\"center\" gap={6} style={{ width: '100%', justifyContent: 'center' }}>\n                      <div\n                        style={{\n                          width: 8,\n                          height: 8,\n                          borderRadius: '50%',\n                          background: '#ff4d4f',\n                        }}\n                      />\n                      <Text\n                        style={{\n                          fontSize: 12,\n                          fontWeight: 500,\n                          color: '#cf1322',\n                        }}\n                      >\n                        高\n                      </Text>\n                      <Text\n                        style={{\n                          fontSize: 15,\n                          fontWeight: 600,\n                          color: '#cf1322',\n                        }}\n                      >\n                        {todoStats.highPriorityCount}\n                      </Text>\n                    </Flex>\n                  </Tooltip>\n                </div>\n\n                {/* 中优先级卡片 */}\n                <div\n                  style={{\n                    background: '#fffbe6',\n                    border: '1px solid #ffe58f',\n                    borderRadius: 8,\n                    padding: '10px 14px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 8,\n                    flex: '1 1 auto',\n                    minWidth: 90,\n                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                  }}\n                >\n                  <Tooltip title={`中优先级任务: ${todoStats.mediumPriorityCount}个`}>\n                    <Flex align=\"center\" gap={6} style={{ width: '100%', justifyContent: 'center' }}>\n                      <div\n                        style={{\n                          width: 8,\n                          height: 8,\n                          borderRadius: '50%',\n                          background: '#faad14',\n                        }}\n                      />\n                      <Text\n                        style={{\n                          fontSize: 12,\n                          fontWeight: 500,\n                          color: '#d48806',\n                        }}\n                      >\n                        中\n                      </Text>\n                      <Text\n                        style={{\n                          fontSize: 15,\n                          fontWeight: 600,\n                          color: '#d48806',\n                        }}\n                      >\n                        {todoStats.mediumPriorityCount}\n                      </Text>\n                    </Flex>\n                  </Tooltip>\n                </div>\n\n                {/* 低优先级卡片 */}\n                <div\n                  style={{\n                    background: '#fafafa',\n                    border: '1px solid #d9d9d9',\n                    borderRadius: 8,\n                    padding: '10px 14px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 8,\n                    flex: '1 1 auto',\n                    minWidth: 90,\n                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                  }}\n                >\n                  <Tooltip title={`低优先级任务: ${todoStats.lowPriorityCount}个`}>\n                    <Flex align=\"center\" gap={6} style={{ width: '100%', justifyContent: 'center' }}>\n                      <div\n                        style={{\n                          width: 8,\n                          height: 8,\n                          borderRadius: '50%',\n                          background: '#8c8c8c',\n                        }}\n                      />\n                      <Text\n                        style={{\n                          fontSize: 12,\n                          fontWeight: 500,\n                          color: '#595959',\n                        }}\n                      >\n                        低\n                      </Text>\n                      <Text\n                        style={{\n                          fontSize: 15,\n                          fontWeight: 600,\n                          color: '#595959',\n                        }}\n                      >\n                        {todoStats.lowPriorityCount}\n                      </Text>\n                    </Flex>\n                  </Tooltip>\n                </div>\n              </Flex>\n            </Flex>\n          </Col>\n        </Row>\n      </div>\n\n      {/* 第二行：标签页 */}\n      <Tabs\n        activeKey={activeTab}\n        onChange={(key) => setActiveTab(key as 'all' | 'pending' | 'completed')}\n        size=\"middle\"\n        style={{ marginBottom: 8 }}\n      >\n        <TabPane tab=\"全部\" key=\"all\" />\n        <TabPane tab=\"待处理\" key=\"pending\" />\n        <TabPane tab=\"已完成\" key=\"completed\" />\n      </Tabs>\n\n      {/* 待办事项列表 */}\n      {error ? (\n        <Alert\n          message=\"TODO数据加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n      ) : (\n        <Spin spinning={loading}>\n          <ProList\n            dataSource={filteredPersonalTasks}\n            renderItem={(item) => {\n              return (\n                <div\n                  className=\"todo-item\"\n                  style={{\n                    padding: '10px 16px',\n                    marginBottom: 12,\n                    borderRadius: 8,\n                    background: '#fff',\n                    opacity: item.status === 1 ? 0.7 : 1,\n                    borderLeft: `3px solid ${\n                      item.status === 1\n                        ? '#52c41a'\n                        : item.priority === 3\n                          ? '#ff4d4f'\n                          : item.priority === 2\n                            ? '#faad14'\n                            : '#8c8c8c'\n                    }`,\n                    boxShadow: '0 1px 4px rgba(0,0,0,0.05)',\n                  }}\n                >\n                  <Flex align=\"center\" gap={12} style={{ width: '100%' }}>\n                    {/* 左侧状态和优先级指示器 */}\n                    <Flex vertical align=\"center\">\n                      {item.status === 1 ? (\n                        <Flex\n                          align=\"center\"\n                          justify=\"center\"\n                          style={{\n                            width: 22,\n                            height: 22,\n                            borderRadius: '50%',\n                            background: '#52c41a',\n                          }}\n                        >\n                          <CheckOutlined\n                            style={{ color: '#fff', fontSize: 12 }}\n                          />\n                        </Flex>\n                      ) : (\n                        <div\n                          style={{\n                            width: 18,\n                            height: 18,\n                            borderRadius: '50%',\n                            border: `2px solid ${\n                              item.priority === 3\n                                ? '#ff4d4f'\n                                : item.priority === 2\n                                  ? '#faad14'\n                                  : '#8c8c8c'\n                            }`,\n                          }}\n                        />\n                      )}\n\n                      <div\n                        style={{\n                          width: 2,\n                          height: 24,\n                          background: '#f0f0f0',\n                          marginTop: 4,\n                        }}\n                      />\n                    </Flex>\n\n                    {/* 任务信息区 */}\n                    <Flex vertical style={{ flex: 1 }}>\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          fontWeight: item.priority === 3 ? 500 : 'normal',\n                          textDecoration:\n                            item.status === 1 ? 'line-through' : 'none',\n                          color: item.status === 1 ? '#8c8c8c' : '#262626',\n                        }}\n                      >\n                        {item.title}\n                      </Text>\n\n                      {/* 显示创建日期 */}\n                      <Space align=\"center\" size={6} style={{ marginTop: 4 }}>\n                        <CalendarOutlined\n                          style={{\n                            fontSize: 12,\n                            color: '#8c8c8c',\n                          }}\n                        />\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          创建于:{' '}\n                          {new Date(item.createdAt).toLocaleDateString('zh-CN')}\n                        </Text>\n                      </Space>\n                    </Flex>\n\n                    {/* 操作按钮区 */}\n                    <Dropdown\n                      trigger={['click']}\n                      menu={{\n                        items: [\n                          {\n                            key: 'complete',\n                            label:\n                              item.status === 1 ? '标记未完成' : '标记完成',\n                            icon: (\n                              <CheckOutlined\n                                style={{\n                                  color:\n                                    item.status === 1 ? '#8c8c8c' : '#52c41a',\n                                  fontSize: 14,\n                                }}\n                              />\n                            ),\n                          },\n                          {\n                            key: 'edit',\n                            label: '编辑任务',\n                            icon: <EditOutlined style={{ color: '#8c8c8c' }} />,\n                          },\n                          {\n                            key: 'delete',\n                            label: '删除任务',\n                            icon: (\n                              <DeleteOutlined style={{ color: '#ff4d4f' }} />\n                            ),\n                            danger: true,\n                          },\n                        ],\n                        onClick: ({ key }) => {\n                          if (key === 'complete') {\n                            handleToggleTodoStatus(item.id);\n                          } else if (key === 'edit') {\n                            setEditingTodoId(item.id);\n                            todoForm.setFieldsValue({\n                              name: item.title,\n                              priority: item.priority,\n                            });\n                            setTodoModalVisible(true);\n                          } else if (key === 'delete') {\n                            handleDeleteTodo(item.id);\n                          }\n                        },\n                      }}\n                    >\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<MoreOutlined />}\n                        style={{ width: 32, height: 32 }}\n                      />\n                    </Dropdown>\n                  </Flex>\n                </div>\n              );\n            }}\n          />\n\n          {/* 待办事项表单模态框 */}\n          <ModalForm\n            title={editingTodoId ? '编辑待办事项' : '新增待办事项'}\n            open={todoModalVisible}\n            onOpenChange={(visible) => {\n              if (!visible) {\n                setTodoModalVisible(false);\n                todoForm.resetFields();\n              }\n            }}\n            form={todoForm}\n            layout=\"vertical\"\n            onFinish={handleAddOrUpdateTodo}\n            autoComplete=\"off\"\n            width={500}\n            modalProps={{\n              centered: true,\n              destroyOnClose: true,\n              maskClosable: false,\n              keyboard: false,\n            }}\n            submitter={{\n              searchConfig: {\n                submitText: editingTodoId ? '更新任务' : '创建任务',\n                resetText: '取消',\n              },\n              submitButtonProps: {\n                style: {\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                },\n                icon: editingTodoId ? <EditOutlined /> : <PlusOutlined />,\n              },\n              resetButtonProps: {\n                style: {\n                  borderColor: '#d9d9d9',\n                },\n              },\n              onReset: () => {\n                setTodoModalVisible(false);\n                todoForm.resetFields();\n              },\n            }}\n            preserve={false}\n          >\n            <Form.Item\n              name=\"name\"\n              label=\"任务名称\"\n              rules={[{ required: true, message: '请输入任务名称' }]}\n            >\n              <Input\n                placeholder=\"请输入任务名称\"\n                size=\"large\"\n                style={{ borderRadius: 6 }}\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"priority\"\n              label=\"优先级\"\n              initialValue={2}\n              rules={[{ required: true, message: '请选择优先级' }]}\n            >\n              <Select\n                size=\"large\"\n                options={[\n                  { value: 3, label: '高优先级' },\n                  { value: 2, label: '中优先级' },\n                  { value: 1, label: '低优先级' },\n                ]}\n                style={{ borderRadius: 6 }}\n              />\n            </Form.Item>\n          </ModalForm>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default TodoManagement;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCguBb;;;2BAAA;;;;;;0CA3tBO;yCAmBA;kDACqC;oFACD;yCACf;;;;;;;;;;YAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;YASxB,MAAM,iBAAgD;;gBACpD,aAAa;gBACb,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB,EAAE;gBACrE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAoB;oBAC5D,mBAAmB;oBACnB,qBAAqB;oBACrB,kBAAkB;oBAClB,YAAY;oBACZ,gBAAgB;oBAChB,sBAAsB;gBACxB;gBACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;gBAElD,WAAW;gBACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBACzD,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;gBAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,QAAQ;gBACR,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EACxC;gBAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAE7C,WAAW;gBACX,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,WAAW;4BACX,SAAS;4BAET,QAAQ,GAAG,CAAC;4BAEZ,8BAA8B;4BAC9B,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;gCACrD,QAAQ,KAAK,CAAC,eAAe;gCAC7B,OAAO,EAAE;4BACX;4BAEA,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;gCACrD,QAAQ,KAAK,CAAC,eAAe;gCAC7B,OAAO;oCACL,mBAAmB;oCACnB,qBAAqB;oCACrB,kBAAkB;oCAClB,YAAY;oCACZ,gBAAgB;oCAChB,sBAAsB;gCACxB;4BACF;4BAEA,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAAC;gCAAc;6BAAa;4BAErE,QAAQ,GAAG,CAAC,8BAA8B;4BAC1C,QAAQ,GAAG,CAAC,4BAA4B;4BAExC,iBAAiB;4BACjB,aAAa;wBACf,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,oBAAoB;4BAClC,SAAS;wBACX,SAAU;4BACR,WAAW;wBACb;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,mBAAmB;gBACnB,MAAM,wBAAwB,AAAC,CAAA,iBAAiB,EAAE,AAAD,EAAG,MAAM,CAAC,CAAC;oBAC1D,SAAS;oBACT,IAAI,cAAc,aAAa,KAAK,MAAM,KAAK,GAAG,OAAO;oBACzD,IAAI,cAAc,eAAe,KAAK,MAAM,KAAK,GAAG,OAAO;oBAE3D,WAAW;oBACX,IACE,cACA,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEzD,OAAO;oBAGT,OAAO;gBACT;gBAEA,WAAW;gBACX,MAAM,yBAAyB,OAAO;oBACpC,IAAI;wBACF,MAAM,OAAO,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;wBAChD,IAAI,CAAC,MACH;wBAGF,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,IAAI;wBAE1C,MAAM,iBAAW,CAAC,UAAU,CAAC,IAAI;4BAAE,QAAQ;wBAAU;wBAErD,SAAS;wBACT,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,KAAK;gCAAE,GAAG,IAAI;gCAAE,QAAQ;4BAAU,IAAI;wBAItD,SAAS;wBACT,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;4BAC5C,aAAa;wBACf,EAAE,OAAO,YAAY;wBACnB,kBAAkB;wBACpB;oBACF,EAAE,OAAO,OAAO;oBACd,iBAAiB;oBACnB;gBACF;gBAEA,MAAM,wBAAwB,OAAO;oBACnC,IAAI;wBACF,IAAI,eAAe;4BACjB,WAAW;4BACX,MAAM,cAAc,MAAM,iBAAW,CAAC,UAAU,CAAC,eAAe;gCAC9D,OAAO,OAAO,IAAI;gCAClB,UAAU,OAAO,QAAQ;4BAC3B;4BAEA,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,gBAAgB,cAAc;wBAGhD,OAAO;4BACL,UAAU;4BACV,MAAM,UAAU,MAAM,iBAAW,CAAC,UAAU,CAAC;gCAC3C,OAAO,OAAO,IAAI;gCAClB,UAAU,OAAO,QAAQ;4BAC3B;4BAEA,iBAAiB;gCAAC;mCAAY;6BAAc;wBAC9C;wBAEA,SAAS;wBACT,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;4BAC5C,aAAa;wBACf,EAAE,OAAO,YAAY;wBACnB,kBAAkB;wBACpB;wBAEA,aAAa;wBACb,oBAAoB;wBACpB,iBAAiB;wBACjB,SAAS,WAAW;oBACtB,EAAE,OAAO,OAAO;oBACd,iBAAiB;oBACnB;gBACF;gBAEA,MAAM,mBAAmB,OAAO;oBAC9B,IAAI;wBACF,MAAM,iBAAW,CAAC,UAAU,CAAC;wBAC7B,iBAAiB,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;wBAE5D,SAAS;wBACT,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;4BAC5C,aAAa;wBACf,EAAE,OAAO,YAAY;wBACnB,kBAAkB;wBACpB;oBACF,EAAE,OAAO,OAAO;oBACd,iBAAiB;oBACnB;gBACF;gBAEA,qBACE,2BAAC,sBAAO;oBACN,OAAM;oBACN,OAAO;wBACL,cAAc;wBACd,QAAQ;wBACR,WAAW;oBACb;oBACA,WAAW;wBACT,cAAc;wBACd,eAAe;oBACjB;oBACA,WAAW;wBACT,SAAS;oBACX;;sCAGA,2BAAC;4BACC,OAAO;gCACL,cAAc;gCACd,SAAS;gCACT,YAAY;gCACZ,cAAc;gCACd,QAAQ;4BACV;;8CAGA,2BAAC,SAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;oCAAE,OAAO;wCAAE,cAAc;oCAAG;8CAC/C,cAAA,2BAAC,SAAG;wCAAC,IAAI;wCAAI,IAAI;wCAAI,IAAI;wCAAI,IAAI;wCAAI,IAAI;kDACvC,cAAA,2BAAC,UAAI;4CAAC,OAAM;4CAAS,KAAK;4CAAI,OAAO;gDAAE,OAAO;4CAAO;;8DACnD,2BAAC,WAAK,CAAC,MAAM;oDACX,aAAY;oDACZ,UAAU;oDACV,sBAAQ,2BAAC,qBAAc;;;;;oDACvB,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,OAAO;wDAAE,MAAM;oDAAE;oDACjB,MAAK;;;;;;8DAGP,2BAAC,YAAM;oDACL,MAAK;oDACL,oBAAM,2BAAC,mBAAY;;;;;oDACnB,SAAS;wDACP,iBAAiB;wDACjB,SAAS,WAAW;wDACpB,oBAAoB;oDACtB;oDACA,OAAO;wDACL,YAAY;wDACZ,aAAa;wDACb,WAAW;wDACX,YAAY;wDACZ,UAAU;oDACZ;oDACA,MAAK;8DACN;;;;;;;;;;;;;;;;;;;;;;8CAQP,2BAAC,SAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;8CACnB,cAAA,2BAAC,SAAG;wCAAC,IAAI;wCAAI,IAAI;wCAAI,IAAI;wCAAI,IAAI;wCAAI,IAAI;kDACvC,cAAA,2BAAC,UAAI;4CAAC,OAAM;4CAAS,SAAQ;4CAAgB,MAAK;4CAAO,KAAK;;8DAE5D,2BAAC;oDACC,OAAO;wDACL,YAAY;wDACZ,QAAQ;wDACR,cAAc;wDACd,SAAS;wDACT,SAAS;wDACT,YAAY;wDACZ,KAAK;wDACL,MAAM;wDACN,UAAU;wDACV,UAAU;wDACV,WAAW;oDACb;8DAEA,cAAA,2BAAC,aAAO;wDACN,OAAO,CAAC,KAAK,EAAE,UAAU,oBAAoB,CAAC,GAAG,EAAE,UAAU,cAAc,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC;kEAEtG,cAAA,2BAAC,UAAI;4DAAC,OAAM;4DAAS,KAAK;4DAAG,OAAO;gEAAE,OAAO;4DAAO;;8EAClD,2BAAC;oEACC,OAAO;wEAAE,UAAU;wEAAI,YAAY;wEAAK,OAAO;oEAAU;8EAC1D;;;;;;8EAGD,2BAAC,cAAQ;oEACP,SAAS,UAAU,oBAAoB;oEACvC,MAAK;oEACL,OAAO;wEAAE,MAAM;wEAAG,UAAU;oEAAG;oEAC/B,aAAY;oEACZ,UAAU;;;;;;8EAEZ,2BAAC;oEACC,OAAO;wEAAE,UAAU;wEAAI,YAAY;wEAAK,OAAO;wEAAW,UAAU;oEAAG;;wEAEtE,UAAU,oBAAoB;wEAAC;;;;;;;;;;;;;;;;;;;;;;;8DAOxC,2BAAC,UAAI;oDAAC,OAAM;oDAAS,KAAK;oDAAG,MAAK;;sEAEhC,2BAAC;4DACC,OAAO;gEACL,YAAY;gEACZ,QAAQ;gEACR,cAAc;gEACd,SAAS;gEACT,SAAS;gEACT,YAAY;gEACZ,KAAK;gEACL,MAAM;gEACN,UAAU;gEACV,WAAW;4DACb;sEAEA,cAAA,2BAAC,aAAO;gEAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,iBAAiB,CAAC,CAAC,CAAC;0EACvD,cAAA,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;oEAAG,OAAO;wEAAE,OAAO;wEAAQ,gBAAgB;oEAAS;;sFAC5E,2BAAC;4EACC,OAAO;gFACL,OAAO;gFACP,QAAQ;gFACR,cAAc;gFACd,YAAY;4EACd;;;;;;sFAEF,2BAAC;4EACC,OAAO;gFACL,UAAU;gFACV,YAAY;gFACZ,OAAO;4EACT;sFACD;;;;;;sFAGD,2BAAC;4EACC,OAAO;gFACL,UAAU;gFACV,YAAY;gFACZ,OAAO;4EACT;sFAEC,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;sEAOpC,2BAAC;4DACC,OAAO;gEACL,YAAY;gEACZ,QAAQ;gEACR,cAAc;gEACd,SAAS;gEACT,SAAS;gEACT,YAAY;gEACZ,KAAK;gEACL,MAAM;gEACN,UAAU;gEACV,WAAW;4DACb;sEAEA,cAAA,2BAAC,aAAO;gEAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,mBAAmB,CAAC,CAAC,CAAC;0EACzD,cAAA,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;oEAAG,OAAO;wEAAE,OAAO;wEAAQ,gBAAgB;oEAAS;;sFAC5E,2BAAC;4EACC,OAAO;gFACL,OAAO;gFACP,QAAQ;gFACR,cAAc;gFACd,YAAY;4EACd;;;;;;sFAEF,2BAAC;4EACC,OAAO;gFACL,UAAU;gFACV,YAAY;gFACZ,OAAO;4EACT;sFACD;;;;;;sFAGD,2BAAC;4EACC,OAAO;gFACL,UAAU;gFACV,YAAY;gFACZ,OAAO;4EACT;sFAEC,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;sEAOtC,2BAAC;4DACC,OAAO;gEACL,YAAY;gEACZ,QAAQ;gEACR,cAAc;gEACd,SAAS;gEACT,SAAS;gEACT,YAAY;gEACZ,KAAK;gEACL,MAAM;gEACN,UAAU;gEACV,WAAW;4DACb;sEAEA,cAAA,2BAAC,aAAO;gEAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,gBAAgB,CAAC,CAAC,CAAC;0EACtD,cAAA,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;oEAAG,OAAO;wEAAE,OAAO;wEAAQ,gBAAgB;oEAAS;;sFAC5E,2BAAC;4EACC,OAAO;gFACL,OAAO;gFACP,QAAQ;gFACR,cAAc;gFACd,YAAY;4EACd;;;;;;sFAEF,2BAAC;4EACC,OAAO;gFACL,UAAU;gFACV,YAAY;gFACZ,OAAO;4EACT;sFACD;;;;;;sFAGD,2BAAC;4EACC,OAAO;gFACL,UAAU;gFACV,YAAY;gFACZ,OAAO;4EACT;sFAEC,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAY7C,2BAAC,UAAI;4BACH,WAAW;4BACX,UAAU,CAAC,MAAQ,aAAa;4BAChC,MAAK;4BACL,OAAO;gCAAE,cAAc;4BAAE;;8CAEzB,2BAAC;oCAAQ,KAAI;mCAAS;;;;;8CACtB,2BAAC;oCAAQ,KAAI;mCAAU;;;;;8CACvB,2BAAC;oCAAQ,KAAI;mCAAU;;;;;;;;;;;wBAIxB,sBACC,2BAAC,WAAK;4BACJ,SAAQ;4BACR,aAAa;4BACb,MAAK;4BACL,QAAQ;4BACR,OAAO;gCAAE,cAAc;4BAAG;;;;;iDAG5B,2BAAC,UAAI;4BAAC,UAAU;;8CACd,2BAAC,sBAAO;oCACN,YAAY;oCACZ,YAAY,CAAC;wCACX,qBACE,2BAAC;4CACC,WAAU;4CACV,OAAO;gDACL,SAAS;gDACT,cAAc;gDACd,cAAc;gDACd,YAAY;gDACZ,SAAS,KAAK,MAAM,KAAK,IAAI,MAAM;gDACnC,YAAY,CAAC,UAAU,EACrB,KAAK,MAAM,KAAK,IACZ,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,UACT,CAAC;gDACF,WAAW;4CACb;sDAEA,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;gDAAI,OAAO;oDAAE,OAAO;gDAAO;;kEAEnD,2BAAC,UAAI;wDAAC,QAAQ;wDAAC,OAAM;;4DAClB,KAAK,MAAM,KAAK,kBACf,2BAAC,UAAI;gEACH,OAAM;gEACN,SAAQ;gEACR,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,YAAY;gEACd;0EAEA,cAAA,2BAAC,oBAAa;oEACZ,OAAO;wEAAE,OAAO;wEAAQ,UAAU;oEAAG;;;;;;;;;;uFAIzC,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,QAAQ,CAAC,UAAU,EACjB,KAAK,QAAQ,KAAK,IACd,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,UACP,CAAC;gEACJ;;;;;;0EAIJ,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,YAAY;oEACZ,WAAW;gEACb;;;;;;;;;;;;kEAKJ,2BAAC,UAAI;wDAAC,QAAQ;wDAAC,OAAO;4DAAE,MAAM;wDAAE;;0EAC9B,2BAAC;gEACC,OAAO;oEACL,UAAU;oEACV,YAAY,KAAK,QAAQ,KAAK,IAAI,MAAM;oEACxC,gBACE,KAAK,MAAM,KAAK,IAAI,iBAAiB;oEACvC,OAAO,KAAK,MAAM,KAAK,IAAI,YAAY;gEACzC;0EAEC,KAAK,KAAK;;;;;;0EAIb,2BAAC,WAAK;gEAAC,OAAM;gEAAS,MAAM;gEAAG,OAAO;oEAAE,WAAW;gEAAE;;kFACnD,2BAAC,uBAAgB;wEACf,OAAO;4EACL,UAAU;4EACV,OAAO;wEACT;;;;;;kFAEF,2BAAC;wEAAK,MAAK;wEAAY,OAAO;4EAAE,UAAU;wEAAG;;4EAAG;4EACzC;4EACJ,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;kEAMnD,2BAAC,cAAQ;wDACP,SAAS;4DAAC;yDAAQ;wDAClB,MAAM;4DACJ,OAAO;gEACL;oEACE,KAAK;oEACL,OACE,KAAK,MAAM,KAAK,IAAI,UAAU;oEAChC,oBACE,2BAAC,oBAAa;wEACZ,OAAO;4EACL,OACE,KAAK,MAAM,KAAK,IAAI,YAAY;4EAClC,UAAU;wEACZ;;;;;;gEAGN;gEACA;oEACE,KAAK;oEACL,OAAO;oEACP,oBAAM,2BAAC,mBAAY;wEAAC,OAAO;4EAAE,OAAO;wEAAU;;;;;;gEAChD;gEACA;oEACE,KAAK;oEACL,OAAO;oEACP,oBACE,2BAAC,qBAAc;wEAAC,OAAO;4EAAE,OAAO;wEAAU;;;;;;oEAE5C,QAAQ;gEACV;6DACD;4DACD,SAAS,CAAC,EAAE,GAAG,EAAE;gEACf,IAAI,QAAQ,YACV,uBAAuB,KAAK,EAAE;qEACzB,IAAI,QAAQ,QAAQ;oEACzB,iBAAiB,KAAK,EAAE;oEACxB,SAAS,cAAc,CAAC;wEACtB,MAAM,KAAK,KAAK;wEAChB,UAAU,KAAK,QAAQ;oEACzB;oEACA,oBAAoB;gEACtB,OAAO,IAAI,QAAQ,UACjB,iBAAiB,KAAK,EAAE;4DAE5B;wDACF;kEAEA,cAAA,2BAAC,YAAM;4DACL,MAAK;4DACL,MAAK;4DACL,oBAAM,2BAAC,mBAAY;;;;;4DACnB,OAAO;gEAAE,OAAO;gEAAI,QAAQ;4DAAG;;;;;;;;;;;;;;;;;;;;;;oCAM3C;;;;;;8CAIF,2BAAC,wBAAS;oCACR,OAAO,gBAAgB,WAAW;oCAClC,MAAM;oCACN,cAAc,CAAC;wCACb,IAAI,CAAC,SAAS;4CACZ,oBAAoB;4CACpB,SAAS,WAAW;wCACtB;oCACF;oCACA,MAAM;oCACN,QAAO;oCACP,UAAU;oCACV,cAAa;oCACb,OAAO;oCACP,YAAY;wCACV,UAAU;wCACV,gBAAgB;wCAChB,cAAc;wCACd,UAAU;oCACZ;oCACA,WAAW;wCACT,cAAc;4CACZ,YAAY,gBAAgB,SAAS;4CACrC,WAAW;wCACb;wCACA,mBAAmB;4CACjB,OAAO;gDACL,YAAY;gDACZ,aAAa;gDACb,WAAW;4CACb;4CACA,MAAM,8BAAgB,2BAAC,mBAAY;;;;uEAAM,2BAAC,mBAAY;;;;;wCACxD;wCACA,kBAAkB;4CAChB,OAAO;gDACL,aAAa;4CACf;wCACF;wCACA,SAAS;4CACP,oBAAoB;4CACpB,SAAS,WAAW;wCACtB;oCACF;oCACA,UAAU;;sDAEV,2BAAC,UAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;4CACN,OAAO;gDAAC;oDAAE,UAAU;oDAAM,SAAS;gDAAU;6CAAE;sDAE/C,cAAA,2BAAC,WAAK;gDACJ,aAAY;gDACZ,MAAK;gDACL,OAAO;oDAAE,cAAc;gDAAE;;;;;;;;;;;sDAI7B,2BAAC,UAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;4CACN,cAAc;4CACd,OAAO;gDAAC;oDAAE,UAAU;oDAAM,SAAS;gDAAS;6CAAE;sDAE9C,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,SAAS;oDACP;wDAAE,OAAO;wDAAG,OAAO;oDAAO;oDAC1B;wDAAE,OAAO;wDAAG,OAAO;oDAAO;oDAC1B;wDAAE,OAAO;wDAAG,OAAO;oDAAO;iDAC3B;gDACD,OAAO;oDAAE,cAAc;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzC;eAtrBM;;oBAgBe,UAAI,CAAC;;;iBAhBpB;gBAwrBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDhuBD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}